<template>
  <div class="sources-page">
    <div class="page-header">
      <h1>Sources Management</h1>
      <p>Manage your Telegram sources for token discovery</p>
    </div>

    <div class="sources-grid">
      <div class="source-card" v-for="source in sources" :key="source.id">
        <div class="source-header">
          <h3>{{ source.name }}</h3>
          <span class="source-type">{{ source.type }}</span>
        </div>

        <div class="source-stats">
          <div class="stat">
            <span class="label">Tokens Found:</span>
            <span class="value">{{ source.tokens_found || 0 }}</span>
          </div>
          <div class="stat">
            <span class="label">Messages:</span>
            <span class="value">{{ source.total_messages || 0 }}</span>
          </div>
        </div>

        <div class="source-status">
          <span :class="['status', source.status]">{{ source.status }}</span>
        </div>
      </div>

      <div class="add-source-card" @click="showAddModal = true">
        <div class="add-icon">+</div>
        <p>Add New Source</p>
      </div>
    </div>

    <!-- Add Source Modal -->
    <div v-if="showAddModal" class="modal-overlay" @click="showAddModal = false">
      <div class="modal" @click.stop>
        <h2>Add New Source</h2>
        <form @submit.prevent="addSource">
          <div class="form-group">
            <label>Name:</label>
            <input v-model="newSource.name" type="text" required>
          </div>
          <div class="form-group">
            <label>Type:</label>
            <select v-model="newSource.type" required>
              <option value="channel">Channel</option>
              <option value="group">Group</option>
              <option value="bot">Bot</option>
            </select>
          </div>
          <div class="form-group">
            <label>Target:</label>
            <input v-model="newSource.target" type="text" placeholder="@channel_username" required>
          </div>
          <div class="form-actions">
            <button type="button" @click="showAddModal = false">Cancel</button>
            <button type="submit">Add Source</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sources',
  data() {
    return {
      sources: [],
      showAddModal: false,
      newSource: {
        name: '',
        type: 'channel',
        target: ''
      }
    }
  },
  async mounted() {
    await this.loadSources()
  },
  methods: {
    async loadSources() {
      try {
        const response = await fetch('/api/sources')
        this.sources = await response.json()
      } catch (error) {
        console.error('Failed to load sources:', error)
      }
    },
    async addSource() {
      try {
        console.log('Adding source:', this.newSource)

        const response = await fetch('/api/sources', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.newSource)
        })

        console.log('Response status:', response.status)

        if (response.ok) {
          const result = await response.json()
          console.log('Source created:', result)
          await this.loadSources()
          this.showAddModal = false
          this.newSource = { name: '', type: 'channel', target: '' }
          alert('Source added successfully!')
        } else {
          const error = await response.text()
          console.error('Server error:', error)
          alert('Failed to add source: ' + error)
        }
      } catch (error) {
        console.error('Failed to add source:', error)
        alert('Failed to add source: ' + error.message)
      }
    }
  }
}
</script>

<style scoped>
.sources-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.source-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.source-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 15px;
}

.source-type {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.source-stats {
  margin-bottom: 15px;
}

.stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.active { background: #e8f5e8; color: #2e7d32; }
.status.error { background: #ffebee; color: #c62828; }
.status.connecting { background: #fff3e0; color: #ef6c00; }

.add-source-card {
  background: #f5f5f5;
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.add-source-card:hover {
  border-color: #1976d2;
  background: #f0f8ff;
}

.add-icon {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 10px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  padding: 30px;
  width: 90%;
  max-width: 500px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.form-actions button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-actions button[type="submit"] {
  background: #1976d2;
  color: white;
}

.form-actions button[type="button"] {
  background: #f5f5f5;
  color: #333;
}
</style>
