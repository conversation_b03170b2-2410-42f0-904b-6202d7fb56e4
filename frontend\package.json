{"name": "demure-web-app-frontend", "version": "1.0.0", "description": "Frontend for Demure Web App - Multi-Source Token Analysis Platform", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "axios": "^1.5.0", "socket.io-client": "^4.7.2", "chart.js": "^4.4.0", "vue-chartjs": "^5.2.0", "element-plus": "^2.3.12", "@element-plus/icons-vue": "^2.1.0", "moment": "^2.29.4", "lodash": "^4.17.21", "file-saver": "^2.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.2", "sass": "^1.66.1"}}