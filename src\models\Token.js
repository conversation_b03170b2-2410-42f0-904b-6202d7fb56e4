const mongoose = require('mongoose');

const tokenSchema = new mongoose.Schema({
  address: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    index: true
  },

  sourceId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Source',
    required: true,
    index: true
  },

  // Basic token information
  symbol: {
    type: String,
    trim: true,
    maxlength: 20
  },

  name: {
    type: String,
    trim: true,
    maxlength: 100
  },

  // Financial data from Soul Scanner
  price: {
    type: Number,
    default: 0
  },

  marketCap: {
    type: String,
    trim: true
  },

  // Initial T-MC (Top Market Cap - ATH)
  topMarketCap: {
    type: String,
    trim: true
  },

  volume24h: {
    type: String,
    trim: true
  },

  holders: {
    type: Number,
    default: 0
  },

  // Additional metrics
  liquidity: {
    type: String,
    trim: true
  },

  fdv: {
    type: String,
    trim: true
  },

  // T-MC Tracking (Top Market Cap at different intervals)
  tMc1h: {
    type: String,
    trim: true,
    default: null
  },

  tMc5h: {
    type: String,
    trim: true,
    default: null
  },

  tMc24h: {
    type: String,
    trim: true,
    default: null
  },

  // Rescan tracking
  rescanStatus: {
    '1h': {
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending'
      },
      attempts: {
        type: Number,
        default: 0
      },
      scheduledAt: Date,
      completedAt: Date,
      lastAttempt: Date
    },
    '5h': {
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending'
      },
      attempts: {
        type: Number,
        default: 0
      },
      scheduledAt: Date,
      completedAt: Date,
      lastAttempt: Date
    },
    '24h': {
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'failed'],
        default: 'pending'
      },
      attempts: {
        type: Number,
        default: 0
      },
      scheduledAt: Date,
      completedAt: Date,
      lastAttempt: Date
    }
  },

  // Risk assessment
  riskScore: {
    type: Number,
    min: 0,
    max: 10,
    default: 5
  },

  riskFactors: [{
    factor: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    description: String
  }],

  // Analysis status
  analysisStatus: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'timeout'],
    default: 'pending',
    index: true
  },

  analysisAttempts: {
    type: Number,
    default: 0
  },

  lastAnalysisAttempt: {
    type: Date
  },

  analysisError: {
    message: String,
    code: String,
    timestamp: Date
  },

  // Soul Scanner raw response
  soulScannerResponse: {
    rawText: String,
    parsedData: mongoose.Schema.Types.Mixed,
    responseTime: Number,
    timestamp: Date
  },

  // Discovery information
  discoveredAt: {
    type: Date,
    default: Date.now,
    index: true
  },

  discoveredMessage: {
    text: String,
    messageId: String,
    chatId: String
  },

  // Performance tracking
  priceHistory: [{
    price: Number,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],

  // Tags and categories
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],

  category: {
    type: String,
    enum: ['meme', 'defi', 'nft', 'gaming', 'utility', 'unknown'],
    default: 'unknown'
  },

  // Metadata
  metadata: {
    website: String,
    twitter: String,
    telegram: String,
    description: String
  },

  // Flags
  isVerified: {
    type: Boolean,
    default: false
  },

  isFlagged: {
    type: Boolean,
    default: false
  },

  flagReason: String,

  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },

  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
tokenSchema.index({ sourceId: 1, discoveredAt: -1 });
tokenSchema.index({ analysisStatus: 1, createdAt: -1 });
tokenSchema.index({ riskScore: 1 });
tokenSchema.index({ price: 1 });
tokenSchema.index({ holders: 1 });
tokenSchema.index({ category: 1 });
tokenSchema.index({ tags: 1 });

// Compound indexes
tokenSchema.index({ sourceId: 1, analysisStatus: 1 });
tokenSchema.index({ discoveredAt: -1, analysisStatus: 1 });

// Virtual for age in hours
tokenSchema.virtual('ageInHours').get(function () {
  return Math.round((Date.now() - this.discoveredAt.getTime()) / (1000 * 60 * 60));
});

// Virtual for price change
tokenSchema.virtual('priceChange24h').get(function () {
  if (this.priceHistory.length < 2) return 0;

  const now = Date.now();
  const dayAgo = now - (24 * 60 * 60 * 1000);

  const recentPrice = this.priceHistory[this.priceHistory.length - 1].price;
  const oldPrice = this.priceHistory.find(p => p.timestamp.getTime() >= dayAgo)?.price;

  if (!oldPrice || oldPrice === 0) return 0;

  return ((recentPrice - oldPrice) / oldPrice) * 100;
});

// Methods
tokenSchema.methods.updatePrice = function (newPrice) {
  this.price = newPrice;
  this.priceHistory.push({
    price: newPrice,
    timestamp: new Date()
  });

  // Keep only last 100 price points
  if (this.priceHistory.length > 100) {
    this.priceHistory = this.priceHistory.slice(-100);
  }

  return this.save();
};

tokenSchema.methods.setAnalysisCompleted = function (data) {
  this.analysisStatus = 'completed';
  this.symbol = data.symbol || this.symbol;
  this.name = data.name || this.name;
  this.price = data.price || this.price;
  this.marketCap = data.marketCap || this.marketCap;
  this.volume24h = data.volume24h || this.volume24h;
  this.holders = data.holders || this.holders;
  this.liquidity = data.liquidity || this.liquidity;
  this.fdv = data.fdv || this.fdv;

  return this.save();
};

tokenSchema.methods.setAnalysisFailed = function (error) {
  this.analysisStatus = 'failed';
  this.analysisError = {
    message: error.message,
    code: error.code || 'UNKNOWN',
    timestamp: new Date()
  };

  return this.save();
};

tokenSchema.methods.incrementAnalysisAttempts = function () {
  this.analysisAttempts += 1;
  this.lastAnalysisAttempt = new Date();

  return this.save();
};

// T-MC Rescan methods
tokenSchema.methods.scheduleRescan = function (interval) {
  const now = new Date();
  const delays = {
    '1h': 60 * 60 * 1000,    // 1 hour
    '5h': 5 * 60 * 60 * 1000, // 5 hours
    '24h': 24 * 60 * 60 * 1000 // 24 hours
  };

  if (!this.rescanStatus) {
    this.rescanStatus = {};
  }

  if (!this.rescanStatus[interval]) {
    this.rescanStatus[interval] = {
      status: 'pending',
      attempts: 0
    };
  }

  this.rescanStatus[interval].scheduledAt = new Date(now.getTime() + delays[interval]);
  this.rescanStatus[interval].status = 'pending';

  return this.save();
};

tokenSchema.methods.startRescan = function (interval) {
  if (!this.rescanStatus[interval]) {
    this.rescanStatus[interval] = { attempts: 0 };
  }

  this.rescanStatus[interval].status = 'processing';
  this.rescanStatus[interval].attempts += 1;
  this.rescanStatus[interval].lastAttempt = new Date();

  return this.save();
};

tokenSchema.methods.completeRescan = function (interval, tMcValue) {
  this.rescanStatus[interval].status = 'completed';
  this.rescanStatus[interval].completedAt = new Date();

  // Set the T-MC value for the specific interval
  this[`tMc${interval}`] = tMcValue;

  return this.save();
};

tokenSchema.methods.failRescan = function (interval) {
  const maxAttempts = 2;

  if (this.rescanStatus[interval].attempts >= maxAttempts) {
    this.rescanStatus[interval].status = 'failed';
    this[`tMc${interval}`] = 'N/A';
  } else {
    this.rescanStatus[interval].status = 'pending';
  }

  return this.save();
};

// Static methods
tokenSchema.statics.getBySource = function (sourceId, limit = 100, skip = 0) {
  return this.find({ sourceId })
    .sort({ discoveredAt: -1 })
    .limit(limit)
    .skip(skip)
    .populate('sourceId');
};

tokenSchema.statics.getPendingAnalysis = function (limit = 10) {
  return this.find({
    analysisStatus: 'pending',
    analysisAttempts: { $lt: 3 }
  })
    .sort({ discoveredAt: 1 })
    .limit(limit);
};

tokenSchema.statics.getTokensForRescan = function (interval, limit = 50) {
  const now = new Date();

  return this.find({
    analysisStatus: 'completed',
    $or: [
      // Tokens that need to be scheduled for this interval
      { [`rescanStatus.${interval}`]: { $exists: false } },
      // Tokens scheduled for this interval that are ready
      {
        [`rescanStatus.${interval}.status`]: 'pending',
        [`rescanStatus.${interval}.scheduledAt`]: { $lte: now },
        [`rescanStatus.${interval}.attempts`]: { $lt: 2 }
      }
    ]
  })
    .sort({ discoveredAt: 1 })
    .limit(limit);
};

tokenSchema.statics.getAnalyticsData = function (sourceId, timeRange = '24h') {
  const timeRanges = {
    '1h': 1 * 60 * 60 * 1000,
    '24h': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000
  };

  const since = new Date(Date.now() - timeRanges[timeRange]);

  return this.aggregate([
    {
      $match: {
        sourceId: mongoose.Types.ObjectId(sourceId),
        discoveredAt: { $gte: since }
      }
    },
    {
      $group: {
        _id: null,
        totalTokens: { $sum: 1 },
        analyzedTokens: {
          $sum: { $cond: [{ $eq: ['$analysisStatus', 'completed'] }, 1, 0] }
        },
        avgRiskScore: { $avg: '$riskScore' },
        avgPrice: { $avg: '$price' },
        avgHolders: { $avg: '$holders' }
      }
    }
  ]);
};

module.exports = mongoose.model('Token', tokenSchema);
