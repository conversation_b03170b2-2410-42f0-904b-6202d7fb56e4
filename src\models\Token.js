const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Token = sequelize.define('Token', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    address: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      }
    },
    source_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'sources',
        key: 'id'
      }
    },

    // Basic token information
    symbol: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: true
    },

    // Financial data
    price: {
      type: DataTypes.DECIMAL(20, 10),
      defaultValue: 0
    },
    market_cap: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    top_market_cap: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    volume_24h: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    holders: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    liquidity: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    fdv: {
      type: DataTypes.STRING(50),
      allowNull: true
    },


    // T-MC Tracking
    t_mc_1h: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    t_mc_5h: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    t_mc_24h: {
      type: DataTypes.STRING(50),
      allowNull: true
    },

    // Risk assessment
    risk_score: {
      type: DataTypes.DECIMAL(3, 1),
      defaultValue: 5,
      validate: {
        min: 0,
        max: 10
      }
    },
    risk_factors: {
      type: DataTypes.JSON,
      defaultValue: []
    },

    // Analysis status
    analysis_status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'timeout'),
      defaultValue: 'pending'
    },
    analysis_attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    last_analysis_attempt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    analysis_error: {
      type: DataTypes.JSON,
      allowNull: true
    },

    // Soul Scanner response
    soul_scanner_response: {
      type: DataTypes.JSON,
      allowNull: true
    },

    // Discovery information
    discovered_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    discovered_message: {
      type: DataTypes.JSON,
      allowNull: true
    },

    // Rescan tracking
    rescan_status: {
      type: DataTypes.JSON,
      defaultValue: {}
    },

    // Performance tracking
    price_history: {
      type: DataTypes.JSON,
      defaultValue: []
    },

    // Tags and categories
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    category: {
      type: DataTypes.ENUM('meme', 'defi', 'nft', 'gaming', 'utility', 'unknown'),
      defaultValue: 'unknown'
    },

    // Metadata
    metadata: {
      type: DataTypes.JSON,
      allowNull: true
    },

    // Flags
    is_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    is_flagged: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    flag_reason: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    tableName: 'tokens',
    indexes: [
      { fields: ['address'] },
      { fields: ['source_id'] },
      { fields: ['analysis_status'] },
      { fields: ['discovered_at'] },
      { fields: ['risk_score'] },
      { fields: ['source_id', 'discovered_at'] },
      { fields: ['source_id', 'analysis_status'] },
      { fields: ['discovered_at', 'analysis_status'] }
    ]
  });

  // Instance methods
  Token.prototype.getAgeInHours = function() {
    return Math.round((Date.now() - this.discovered_at.getTime()) / (1000 * 60 * 60));
  };

  Token.prototype.getPriceChange24h = function() {
    if (!this.price_history || this.price_history.length < 2) return 0;

    const now = Date.now();
    const dayAgo = now - (24 * 60 * 60 * 1000);

    const recentPrice = this.price_history[this.price_history.length - 1].price;
    const oldPrice = this.price_history.find(p => new Date(p.timestamp).getTime() >= dayAgo)?.price;

    if (!oldPrice || oldPrice === 0) return 0;

    return ((recentPrice - oldPrice) / oldPrice) * 100;
  };

  Token.prototype.updatePrice = async function(newPrice) {
    this.price = newPrice;

    if (!this.price_history) this.price_history = [];
    this.price_history.push({
      price: newPrice,
      timestamp: new Date()
    });

    // Keep only last 100 price points
    if (this.price_history.length > 100) {
      this.price_history = this.price_history.slice(-100);
    }

    return await this.save();
  };

  Token.prototype.setAnalysisCompleted = async function(data) {
    this.analysis_status = 'completed';
    this.symbol = data.symbol || this.symbol;
    this.name = data.name || this.name;
    this.price = data.price || this.price;
    this.market_cap = data.marketCap || this.market_cap;
    this.volume_24h = data.volume24h || this.volume_24h;
    this.holders = data.holders || this.holders;
    this.liquidity = data.liquidity || this.liquidity;
    this.fdv = data.fdv || this.fdv;

    return await this.save();
  };

  Token.prototype.setAnalysisFailed = async function(error) {
    this.analysis_status = 'failed';
    this.analysis_error = {
      message: error.message,
      code: error.code || 'UNKNOWN',
      timestamp: new Date()
    };

    return await this.save();
  };

  Token.prototype.incrementAnalysisAttempts = async function() {
    this.analysis_attempts += 1;
    this.last_analysis_attempt = new Date();

    return await this.save();
  };

  // T-MC Rescan methods
  Token.prototype.scheduleRescan = async function(interval) {
    const now = new Date();
    const delays = {
      '1h': 60 * 60 * 1000,    // 1 hour
      '5h': 5 * 60 * 60 * 1000, // 5 hours
      '24h': 24 * 60 * 60 * 1000 // 24 hours
    };

    if (!this.rescan_status) {
      this.rescan_status = {};
    }

    if (!this.rescan_status[interval]) {
      this.rescan_status[interval] = {
        status: 'pending',
        attempts: 0
      };
    }

    this.rescan_status[interval].scheduledAt = new Date(now.getTime() + delays[interval]);
    this.rescan_status[interval].status = 'pending';

    return await this.save();
  };

  Token.prototype.startRescan = async function(interval) {
    if (!this.rescan_status[interval]) {
      this.rescan_status[interval] = { attempts: 0 };
    }

    this.rescan_status[interval].status = 'processing';
    this.rescan_status[interval].attempts += 1;
    this.rescan_status[interval].lastAttempt = new Date();

    return await this.save();
  };

  Token.prototype.completeRescan = async function(interval, tMcValue) {
    this.rescan_status[interval].status = 'completed';
    this.rescan_status[interval].completedAt = new Date();

    // Set the T-MC value for the specific interval
    this[`t_mc_${interval}`] = tMcValue;

    return await this.save();
  };

  Token.prototype.failRescan = async function(interval) {
    const maxAttempts = 2;

    if (this.rescan_status[interval].attempts >= maxAttempts) {
      this.rescan_status[interval].status = 'failed';
      this[`t_mc_${interval}`] = 'N/A';
    } else {
      this.rescan_status[interval].status = 'pending';
    }

    return await this.save();
  };

  // Class methods
  Token.getBySource = function(sourceId, limit = 100, skip = 0) {
    return Token.findAll({
      where: { source_id: sourceId },
      order: [['discovered_at', 'DESC']],
      limit: limit,
      offset: skip,
      include: [{ model: sequelize.models.Source, as: 'source' }]
    });
  };

  Token.getPendingAnalysis = function(limit = 10) {
    return Token.findAll({
      where: {
        analysis_status: 'pending',
        analysis_attempts: { [sequelize.Sequelize.Op.lt]: 3 }
      },
      order: [['discovered_at', 'ASC']],
      limit: limit
    });
  };

  Token.getTokensForRescan = function(interval, limit = 50) {
    const now = new Date();

    return Token.findAll({
      where: {
        analysis_status: 'completed',
        [sequelize.Sequelize.Op.or]: [
          // Tokens that need to be scheduled for this interval
          { [`rescan_status.${interval}`]: null },
          // Tokens scheduled for this interval that are ready
          sequelize.literal(`JSON_EXTRACT(rescan_status, '$.${interval}.status') = 'pending' AND JSON_EXTRACT(rescan_status, '$.${interval}.scheduledAt') <= '${now.toISOString()}' AND JSON_EXTRACT(rescan_status, '$.${interval}.attempts') < 2`)
        ]
      },
      order: [['discovered_at', 'ASC']],
      limit: limit
    });
  };

  Token.getAnalyticsData = async function(sourceId, timeRange = '24h') {
    const timeRanges = {
      '1h': 1 * 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000
    };

    const since = new Date(Date.now() - timeRanges[timeRange]);

    const result = await Token.findAll({
      where: {
        source_id: sourceId,
        discovered_at: { [sequelize.Sequelize.Op.gte]: since }
      },
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalTokens'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN analysis_status = "completed" THEN 1 ELSE 0 END')), 'analyzedTokens'],
        [sequelize.fn('AVG', sequelize.col('risk_score')), 'avgRiskScore'],
        [sequelize.fn('AVG', sequelize.col('price')), 'avgPrice'],
        [sequelize.fn('AVG', sequelize.col('holders')), 'avgHolders']
      ],
      raw: true
    });

    return result[0];
  };

  return Token;
};
