const Token = require('../models/Token');
const Logger = require('../utils/logger');

class RescanService {
  constructor(telegramPool, io) {
    this.telegramPool = telegramPool;
    this.io = io;
    this.logger = new Logger();
    this.isProcessing = false;
    this.soulScannerBot = process.env.SOUL_SCANNER_BOT_USERNAME || '@soul_scanner_bot';
    this.responseTimeout = parseInt(process.env.SOUL_SCANNER_TIMEOUT) || 30000;
    
    // Start the rescan processor
    this.startRescanProcessor();
  }

  async startRescanProcessor() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.logger.info('Starting T-MC rescan processor');

    while (true) {
      try {
        // Process each interval
        const intervals = ['1h', '5h', '24h'];
        
        for (const interval of intervals) {
          await this.processRescanInterval(interval);
        }
        
        // Wait 5 minutes before next check
        await this.delay(5 * 60 * 1000);
        
      } catch (error) {
        this.logger.error('Error in rescan processor:', error);
        await this.delay(10000); // Wait 10 seconds on error
      }
    }
  }

  async processRescanInterval(interval) {
    try {
      // Get tokens ready for this interval rescan
      const tokens = await Token.getTokensForRescan(interval, 10);
      
      if (tokens.length === 0) {
        return;
      }

      this.logger.info(`Processing ${tokens.length} tokens for ${interval} rescan`);

      for (const token of tokens) {
        try {
          await this.processTokenRescan(token, interval);
          
          // Rate limiting delay
          await this.telegramPool.waitForRateLimit();
          
        } catch (error) {
          this.logger.error(`Failed to rescan token ${token.address} for ${interval}:`, error);
          await token.failRescan(interval);
        }
      }
      
    } catch (error) {
      this.logger.error(`Error processing ${interval} rescan interval:`, error);
    }
  }

  async processTokenRescan(token, interval) {
    const startTime = Date.now();

    try {
      // Start the rescan
      await token.startRescan(interval);

      this.logger.info(`Rescanning token for T-MC: ${token.address}`, {
        interval,
        attempt: token.rescanStatus[interval]?.attempts || 1
      });

      // Emit processing update
      this.io.emit('token-rescanning', {
        tokenId: token._id,
        address: token.address,
        interval,
        status: 'processing'
      });

      // Send to Soul Scanner Bot for rescan
      const tMcResult = await this.rescanWithSoulScanner(token.address);
      
      // Save the T-MC result
      await token.completeRescan(interval, tMcResult);

      const processingTime = Date.now() - startTime;
      this.logger.info(`Token rescan completed: ${token.address}`, {
        interval,
        tMcValue: tMcResult,
        processingTime: `${processingTime}ms`
      });

      // Emit completion
      this.io.emit('token-rescanned', {
        tokenId: token._id,
        address: token.address,
        interval,
        status: 'completed',
        tMcValue: tMcResult,
        processingTime
      });

    } catch (error) {
      this.logger.error(`Failed to rescan token ${token.address} for ${interval}:`, error);
      
      await token.failRescan(interval);
      
      // Emit failure
      this.io.emit('token-rescanned', {
        tokenId: token._id,
        address: token.address,
        interval,
        status: 'failed',
        error: error.message
      });
    }
  }

  async rescanWithSoulScanner(tokenAddress) {
    const client = this.telegramPool.getAvailableClient();
    
    try {
      // Send token address to Soul Scanner Bot
      await client.sendMessage(this.soulScannerBot, { message: tokenAddress });
      
      this.logger.debug(`Sent ${tokenAddress} to Soul Scanner Bot for rescan`);
      
      // Wait for response
      const response = await this.waitForSoulScannerResponse(client, tokenAddress);
      
      // Parse only the T-MC from the response
      const tMcValue = this.extractTMcFromResponse(response);
      
      return tMcValue;
      
    } catch (error) {
      this.logger.error(`Soul Scanner rescan failed for ${tokenAddress}:`, error);
      throw error;
    }
  }

  async waitForSoulScannerResponse(client, tokenAddress) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        client.removeEventHandler(handler);
        reject(new Error('Soul Scanner response timeout'));
      }, this.responseTimeout);

      const handler = (event) => {
        if (!event.message || !event.message.text) return;
        
        const message = event.message;
        
        // Check if message is from Soul Scanner Bot
        if (this.isSoulScannerResponse(message, tokenAddress)) {
          clearTimeout(timeout);
          client.removeEventHandler(handler);
          resolve(message.text);
        }
      };

      client.addEventHandler(handler);
    });
  }

  isSoulScannerResponse(message, tokenAddress) {
    // Check if message is from Soul Scanner Bot
    const isSoulScanner = message.fromId && 
                         message.fromId.userId && 
                         message.fromId.userId.toString() === this.soulScannerBot.replace('@', '');
    
    // Check if message contains the token address or T-MC data
    const containsToken = message.text.includes(tokenAddress) || 
                         message.text.includes('T-MC:') ||
                         message.text.includes('Top Market Cap:');
    
    return isSoulScanner && containsToken;
  }

  extractTMcFromResponse(responseText) {
    try {
      const lines = responseText.split('\n');
      
      for (const line of lines) {
        const cleanLine = line.trim();
        
        // Extract T-MC (Top Market Cap)
        if (cleanLine.includes('T-MC:')) {
          return this.extractValue(cleanLine, 'T-MC:');
        }
        
        // Alternative patterns for T-MC
        if (cleanLine.includes('Top Market Cap:')) {
          return this.extractValue(cleanLine, 'Top Market Cap:');
        }
        
        if (cleanLine.includes('ATH Market Cap:')) {
          return this.extractValue(cleanLine, 'ATH Market Cap:');
        }
      }
      
      // If no T-MC found, return null
      return null;
      
    } catch (error) {
      this.logger.error('Error extracting T-MC from response:', error);
      return null;
    }
  }

  extractValue(line, prefix) {
    const index = line.indexOf(prefix);
    if (index === -1) return null;
    
    return line.substring(index + prefix.length).trim().replace(/^[:\-\s]+/, '');
  }

  async getRescanStats() {
    try {
      const stats = await Token.aggregate([
        {
          $group: {
            _id: null,
            totalTokens: { $sum: 1 },
            completed1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'completed'] }, 1, 0] }
            },
            completed5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'completed'] }, 1, 0] }
            },
            completed24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'completed'] }, 1, 0] }
            },
            pending1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'pending'] }, 1, 0] }
            },
            pending5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'pending'] }, 1, 0] }
            },
            pending24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'pending'] }, 1, 0] }
            },
            failed1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'failed'] }, 1, 0] }
            },
            failed5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'failed'] }, 1, 0] }
            },
            failed24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'failed'] }, 1, 0] }
            }
          }
        }
      ]);

      return stats[0] || {
        totalTokens: 0,
        completed1h: 0, completed5h: 0, completed24h: 0,
        pending1h: 0, pending5h: 0, pending24h: 0,
        failed1h: 0, failed5h: 0, failed24h: 0
      };
      
    } catch (error) {
      this.logger.error('Error getting rescan stats:', error);
      return null;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = RescanService;
