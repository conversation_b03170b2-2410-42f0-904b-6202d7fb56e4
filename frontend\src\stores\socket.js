import { defineStore } from 'pinia'
import { ref } from 'vue'
import { io } from 'socket.io-client'

export const useSocketStore = defineStore('socket', () => {
  const socket = ref(null)
  const connected = ref(false)
  const listeners = ref(new Map())
  
  const connect = async () => {
    if (socket.value?.connected) return
    
    socket.value = io({
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      timeout: 20000
    })
    
    socket.value.on('connect', () => {
      connected.value = true
      console.log('Socket connected')
    })
    
    socket.value.on('disconnect', () => {
      connected.value = false
      console.log('Socket disconnected')
    })
    
    socket.value.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
    })
    
    // Set up event listeners
    setupEventListeners()
  }
  
  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect()
      socket.value = null
      connected.value = false
    }
  }
  
  const emit = (event, data) => {
    if (socket.value?.connected) {
      socket.value.emit(event, data)
    }
  }
  
  const on = (event, callback) => {
    if (!listeners.value.has(event)) {
      listeners.value.set(event, [])
    }
    listeners.value.get(event).push(callback)
    
    if (socket.value) {
      socket.value.on(event, callback)
    }
  }
  
  const off = (event, callback) => {
    const eventListeners = listeners.value.get(event)
    if (eventListeners) {
      const index = eventListeners.indexOf(callback)
      if (index > -1) {
        eventListeners.splice(index, 1)
      }
    }
    
    if (socket.value) {
      socket.value.off(event, callback)
    }
  }
  
  const setupEventListeners = () => {
    // Re-register all stored listeners
    for (const [event, callbacks] of listeners.value) {
      callbacks.forEach(callback => {
        socket.value.on(event, callback)
      })
    }
  }
  
  const subscribeToSource = (sourceId) => {
    emit('subscribe-source', sourceId)
  }
  
  const unsubscribeFromSource = (sourceId) => {
    emit('unsubscribe-source', sourceId)
  }
  
  return {
    socket,
    connected,
    connect,
    disconnect,
    emit,
    on,
    off,
    subscribeToSource,
    unsubscribeFromSource
  }
})
