<template>
  <div class="source-detail-page">
    <div class="page-header">
      <button @click="$router.back()" class="back-btn">← Back</button>
      <h1>{{ source.name || 'Source Details' }}</h1>
    </div>

    <div v-if="source.id" class="source-info">
      <div class="info-card">
        <h3>Source Information</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>Name:</label>
            <span>{{ source.name }}</span>
          </div>
          <div class="info-item">
            <label>Type:</label>
            <span>{{ source.type }}</span>
          </div>
          <div class="info-item">
            <label>Target:</label>
            <span>{{ source.target }}</span>
          </div>
          <div class="info-item">
            <label>Status:</label>
            <span :class="['status', source.status]">{{ source.status }}</span>
          </div>
        </div>
      </div>

      <div class="stats-card">
        <h3>Statistics</h3>
        <div class="stats-grid">
          <div class="stat">
            <div class="stat-value">{{ source.tokens_found || 0 }}</div>
            <div class="stat-label">Tokens Found</div>
          </div>
          <div class="stat">
            <div class="stat-value">{{ source.tokens_analyzed || 0 }}</div>
            <div class="stat-label">Tokens Analyzed</div>
          </div>
          <div class="stat">
            <div class="stat-value">{{ source.total_messages || 0 }}</div>
            <div class="stat-label">Total Messages</div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading">
      <p>Loading source details...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SourceDetail',
  data() {
    return {
      source: {}
    }
  },
  async mounted() {
    await this.loadSource()
  },
  methods: {
    async loadSource() {
      try {
        const id = this.$route.params.id
        const response = await fetch(`/api/sources/${id}`)
        this.source = await response.json()
      } catch (error) {
        console.error('Failed to load source:', error)
      }
    }
  }
}
</script>

<style scoped>
.source-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.back-btn {
  background: #f5f5f5;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.source-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-card, .stats-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-grid {
  display: grid;
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
}

.info-item label {
  font-weight: bold;
  color: #666;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.stat {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.active { background: #e8f5e8; color: #2e7d32; }
.status.error { background: #ffebee; color: #c62828; }

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}
</style>
