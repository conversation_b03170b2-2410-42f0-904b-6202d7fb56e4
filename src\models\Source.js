const mongoose = require('mongoose');

const sourceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  type: {
    type: String,
    required: true,
    enum: ['bot', 'channel', 'group'],
    lowercase: true
  },
  
  target: {
    type: String,
    required: true,
    trim: true
  },
  
  telegramAccount: {
    type: String,
    required: true,
    trim: true
  },
  
  extractionPattern: {
    type: String,
    default: '[A-Za-z0-9]{32,}pump'
  },
  
  isActive: {
    type: Boolean,
    default: true
  },
  
  configuration: {
    filterEndsWith: {
      type: String,
      default: 'pump'
    },
    minTokenLength: {
      type: Number,
      default: 32
    },
    maxTokenLength: {
      type: Number,
      default: 50
    },
    enableDuplicateFilter: {
      type: Boolean,
      default: true
    }
  },
  
  stats: {
    tokensFound: {
      type: Number,
      default: 0
    },
    tokensAnalyzed: {
      type: Number,
      default: 0
    },
    lastActivity: {
      type: Date,
      default: null
    },
    totalMessages: {
      type: Number,
      default: 0
    },
    errorCount: {
      type: Number,
      default: 0
    }
  },
  
  status: {
    type: String,
    enum: ['active', 'paused', 'error', 'connecting'],
    default: 'connecting'
  },
  
  lastError: {
    message: String,
    timestamp: Date,
    code: String
  },
  
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
sourceSchema.index({ telegramAccount: 1 });
sourceSchema.index({ type: 1 });
sourceSchema.index({ isActive: 1 });
sourceSchema.index({ createdAt: -1 });

// Virtual for tokens per hour
sourceSchema.virtual('tokensPerHour').get(function() {
  if (!this.stats.lastActivity || !this.createdAt) return 0;
  
  const hoursSinceCreation = (Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60);
  return hoursSinceCreation > 0 ? Math.round(this.stats.tokensFound / hoursSinceCreation * 100) / 100 : 0;
});

// Virtual for success rate
sourceSchema.virtual('successRate').get(function() {
  if (this.stats.tokensFound === 0) return 0;
  return Math.round((this.stats.tokensAnalyzed / this.stats.tokensFound) * 100);
});

// Methods
sourceSchema.methods.incrementTokensFound = function() {
  this.stats.tokensFound += 1;
  this.stats.lastActivity = new Date();
  return this.save();
};

sourceSchema.methods.incrementTokensAnalyzed = function() {
  this.stats.tokensAnalyzed += 1;
  return this.save();
};

sourceSchema.methods.incrementMessages = function() {
  this.stats.totalMessages += 1;
  return this.save();
};

sourceSchema.methods.recordError = function(error) {
  this.stats.errorCount += 1;
  this.lastError = {
    message: error.message,
    timestamp: new Date(),
    code: error.code || 'UNKNOWN'
  };
  this.status = 'error';
  return this.save();
};

sourceSchema.methods.setActive = function() {
  this.status = 'active';
  this.isActive = true;
  return this.save();
};

sourceSchema.methods.setPaused = function() {
  this.status = 'paused';
  this.isActive = false;
  return this.save();
};

// Static methods
sourceSchema.statics.getActiveByAccount = function(telegramAccount) {
  return this.find({ 
    telegramAccount, 
    isActive: true, 
    status: { $ne: 'error' } 
  });
};

sourceSchema.statics.getStatsSummary = function() {
  return this.aggregate([
    {
      $group: {
        _id: null,
        totalSources: { $sum: 1 },
        activeSources: { 
          $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] } 
        },
        totalTokensFound: { $sum: '$stats.tokensFound' },
        totalTokensAnalyzed: { $sum: '$stats.tokensAnalyzed' },
        totalMessages: { $sum: '$stats.totalMessages' }
      }
    }
  ]);
};

module.exports = mongoose.model('Source', sourceSchema);
