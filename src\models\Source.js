const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Source = sequelize.define('Source', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    type: {
      type: DataTypes.ENUM('bot', 'channel', 'group'),
      allowNull: false
    },
    target: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    telegram_account: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    extraction_pattern: {
      type: DataTypes.STRING(255),
      defaultValue: '[A-Za-z0-9]{32,}pump'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },

    // Configuration stored as JSON
    configuration: {
      type: DataTypes.JSON,
      defaultValue: {
        filterEndsWith: 'pump',
        minTokenLength: 32,
        maxTokenLength: 50,
        enableDuplicateFilter: true
      }
    },


    // Statistics
    tokens_found: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    tokens_analyzed: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    total_messages: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    error_count: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    last_activity: {
      type: DataTypes.DATE,
      allowNull: true
    },

    status: {
      type: DataTypes.ENUM('active', 'paused', 'error', 'connecting'),
      defaultValue: 'connecting'
    },

    last_error: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    tableName: 'sources',
    indexes: [
      { fields: ['telegram_account'] },
      { fields: ['type'] },
      { fields: ['is_active'] },
      { fields: ['created_at'] }
    ]
  });

  // Instance methods
  Source.prototype.incrementTokensFound = async function() {
    this.tokens_found += 1;
    this.last_activity = new Date();
    return await this.save();
  };

  Source.prototype.incrementTokensAnalyzed = async function() {
    this.tokens_analyzed += 1;
    return await this.save();
  };

  Source.prototype.incrementMessages = async function() {
    this.total_messages += 1;
    return await this.save();
  };

  Source.prototype.recordError = async function(error) {
    this.error_count += 1;
    this.last_error = {
      message: error.message,
      timestamp: new Date(),
      code: error.code || 'UNKNOWN'
    };
    this.status = 'error';
    return await this.save();
  };

  Source.prototype.setActive = async function() {
    this.status = 'active';
    this.is_active = true;
    return await this.save();
  };

  Source.prototype.setPaused = async function() {
    this.status = 'paused';
    this.is_active = false;
    return await this.save();
  };

  // Virtual getters
  Source.prototype.getTokensPerHour = function() {
    if (!this.last_activity || !this.created_at) return 0;
    const hoursSinceCreation = (Date.now() - this.created_at.getTime()) / (1000 * 60 * 60);
    return hoursSinceCreation > 0 ? Math.round(this.tokens_found / hoursSinceCreation * 100) / 100 : 0;
  };

  Source.prototype.getSuccessRate = function() {
    if (this.tokens_found === 0) return 0;
    return Math.round((this.tokens_analyzed / this.tokens_found) * 100);
  };

  // Class methods
  Source.getActiveByAccount = function(telegramAccount) {
    return Source.findAll({
      where: {
        telegram_account: telegramAccount,
        is_active: true,
        status: { [sequelize.Sequelize.Op.ne]: 'error' }
      }
    });
  };

  Source.getStatsSummary = async function() {
    const result = await Source.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalSources'],
        [sequelize.fn('SUM', sequelize.literal('CASE WHEN is_active = 1 THEN 1 ELSE 0 END')), 'activeSources'],
        [sequelize.fn('SUM', sequelize.col('tokens_found')), 'totalTokensFound'],
        [sequelize.fn('SUM', sequelize.col('tokens_analyzed')), 'totalTokensAnalyzed'],
        [sequelize.fn('SUM', sequelize.col('total_messages')), 'totalMessages']
      ],
      raw: true
    });
    return result[0];
  };

  return Source;
};
