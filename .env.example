# Server Configuration
PORT=3002
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/demure-web-app

# Redis (for queue management)
REDIS_URL=redis://localhost:6379

# Telegram API Configuration
# Get these from https://my.telegram.org/apps
TELEGRAM_API_ID_1=********
TELEGRAM_API_HASH_1=89262a88085d83efacfc8d742ab4865a
TELEGRAM_PHONE_1=+************
TELEGRAM_SESSION_1=telegram_1

TELEGRAM_API_ID_2=********
TELEGRAM_API_HASH_2=8f915a912f40f476a93b11978340e215
TELEGRAM_PHONE_2=+************
TELEGRAM_SESSION_2=telegram_2

# Add more accounts as needed
# TELEGRAM_API_ID_3=your_api_id_3
# TELEGRAM_API_HASH_3=your_api_hash_3
# TELEGRAM_PHONE_3=+**********
# TELEGRAM_SESSION_3=your_session_string_3

# Soul Scanner Bot Configuration
SOUL_SCANNER_BOT_USERNAME=@soul_scanner_bot
SOUL_SCANNER_TIMEOUT=30000

# Rate Limiting
REQUESTS_PER_MINUTE=30
SOUL_SCANNER_DELAY=2000

# Logging
LOG_LEVEL=info
LOG_FILE=logs/demure.log

# Security
JWT_SECRET=your_jwt_secret_here
CORS_ORIGIN=http://localhost:3000

# Analytics
EXCEL_EXPORT_LIMIT=10000
TOKEN_RETENTION_DAYS=30
