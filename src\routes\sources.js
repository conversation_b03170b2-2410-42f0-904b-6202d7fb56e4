const express = require('express');
const Logger = require('../utils/logger');

function createSourcesRouter(sourceManager) {
  const router = express.Router();
  const logger = new Logger();

  // Get all sources
  router.get('/', async (req, res) => {
    try {
      // Use global Source model
      const sources = await global.Source.findAll({
        order: [['created_at', 'DESC']]
      });

      // Convert to plain objects and fix field names for frontend
      const sourcesData = sources.map(source => ({
        id: source.id,
        name: source.name,
        type: source.type,
        target: source.target,
        telegram_account: source.telegram_account,
        is_active: source.is_active,
        status: source.status,
        tokens_found: source.tokens_found,
        tokens_analyzed: source.tokens_analyzed,
        total_messages: source.total_messages,
        created_at: source.created_at,
        updated_at: source.updated_at
      }));

      res.json(sourcesData);
    } catch (error) {
      logger.error('Error fetching sources:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch sources'
      });
    }
  });

  // Get source by ID
  router.get('/:id', async (req, res) => {
    try {
      const source = await global.Source.findByPk(req.params.id);

      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      // Convert to frontend format
      const responseData = {
        id: source.id,
        name: source.name,
        type: source.type,
        target: source.target,
        telegram_account: source.telegram_account,
        is_active: source.is_active,
        status: source.status,
        tokens_found: source.tokens_found || 0,
        tokens_analyzed: source.tokens_analyzed || 0,
        total_messages: source.total_messages || 0,
        created_at: source.created_at,
        updated_at: source.updated_at
      };

      res.json(responseData);
    } catch (error) {
      logger.error('Error fetching source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch source'
      });
    }
  });

  // Create new source
  router.post('/', async (req, res) => {
    try {
      logger.info('POST /api/sources - Request body:', req.body);
      const { name, type, target } = req.body;

      // Validate required fields
      if (!name || !type || !target) {
        logger.error('Missing required fields:', { name, type, target });
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: name, type, target'
        });
      }

      // Create source with default telegram account (first available)
      const sourceData = {
        name: name.trim(),
        type: type.toLowerCase(),
        target: target.trim(),
        telegram_account: 'account_1', // Default for now
        is_active: true,
        status: 'connecting'
      };

      logger.info('Creating source with data:', sourceData);
      const source = await global.Source.create(sourceData);
      logger.info('Source created successfully:', source.id);

      // Convert to frontend format
      const responseData = {
        id: source.id,
        name: source.name,
        type: source.type,
        target: source.target,
        telegram_account: source.telegram_account,
        is_active: source.is_active,
        status: source.status,
        tokens_found: source.tokens_found || 0,
        tokens_analyzed: source.tokens_analyzed || 0,
        total_messages: source.total_messages || 0,
        created_at: source.created_at,
        updated_at: source.updated_at
      };

      logger.info('Sending response:', responseData);
      res.status(201).json(responseData);
    } catch (error) {
      logger.error('Error creating source:', error);
      res.status(500).json({
        success: false,
        error: error.message || 'Failed to create source'
      });
    }
  });

  // Update source
  router.put('/:id', async (req, res) => {
    try {
      const source = await Source.findById(req.params.id);

      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      // Update allowed fields
      const allowedUpdates = ['name', 'extractionPattern', 'configuration'];
      const updates = {};

      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      Object.assign(source, updates);
      await source.save();

      res.json({
        success: true,
        data: source,
        message: 'Source updated successfully'
      });
    } catch (error) {
      logger.error('Error updating source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update source'
      });
    }
  });

  // Toggle source active status
  router.patch('/:id/toggle', async (req, res) => {
    try {
      const source = await Source.findById(req.params.id);

      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      if (source.isActive) {
        await sourceManager.stopListening(source._id.toString());
        await source.setPaused();
      } else {
        await sourceManager.startListening(source);
        await source.setActive();
      }

      res.json({
        success: true,
        data: source,
        message: `Source ${source.isActive ? 'activated' : 'paused'} successfully`
      });
    } catch (error) {
      logger.error('Error toggling source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle source status'
      });
    }
  });

  // Delete source
  router.delete('/:id', async (req, res) => {
    try {
      const source = await sourceManager.removeSource(req.params.id);

      res.json({
        success: true,
        data: source,
        message: 'Source removed successfully'
      });
    } catch (error) {
      logger.error('Error deleting source:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete source'
      });
    }
  });

  // Get source statistics
  router.get('/:id/stats', async (req, res) => {
    try {
      const timeRange = req.query.timeRange || '24h';
      const analytics = await sourceManager.getSourceAnalytics(req.params.id, timeRange);

      res.json({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching source stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch source statistics'
      });
    }
  });

  // Get available Telegram accounts
  router.get('/telegram/accounts', (req, res) => {
    try {
      const accounts = sourceManager.telegramPool.getStatus();

      res.json({
        success: true,
        data: {
          accounts: Object.keys(accounts.clients).map(id => ({
            id,
            isActive: accounts.clients[id].isActive,
            phoneNumber: accounts.clients[id].phoneNumber,
            requestCount: accounts.clients[id].requestCount,
            errorCount: accounts.clients[id].errorCount,
            lastUsed: accounts.clients[id].lastUsed
          }))
        }
      });
    } catch (error) {
      logger.error('Error fetching Telegram accounts:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch Telegram accounts'
      });
    }
  });

  return router;
}

module.exports = createSourcesRouter;
