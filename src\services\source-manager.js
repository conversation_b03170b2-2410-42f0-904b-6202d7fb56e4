const { NewMessage } = require('telegram');
const Source = require('../models/Source');
const Token = require('../models/Token');
const Logger = require('../utils/logger');

class SourceManager {
  constructor(telegramPool, io) {
    this.telegramPool = telegramPool;
    this.io = io;
    this.logger = new Logger();
    this.activeSources = new Map();
    this.messageHandlers = new Map();
    
    // Load existing sources on startup
    this.loadExistingSources();
  }

  async loadExistingSources() {
    try {
      const sources = await Source.find({ isActive: true });
      this.logger.info(`Loading ${sources.length} existing sources...`);
      
      for (const source of sources) {
        await this.startListening(source);
      }
      
      this.logger.info(`Successfully loaded ${this.activeSources.size} sources`);
    } catch (error) {
      this.logger.error('Failed to load existing sources:', error);
    }
  }

  async addSource(sourceConfig) {
    try {
      // Validate configuration
      this.validateSourceConfig(sourceConfig);
      
      // Create source in database
      const source = new Source({
        name: sourceConfig.name,
        type: sourceConfig.type,
        target: sourceConfig.target,
        telegramAccount: sourceConfig.telegramAccount,
        extractionPattern: sourceConfig.extractionPattern || '[A-Za-z0-9]{32,}pump',
        configuration: {
          filterEndsWith: sourceConfig.filterEndsWith || 'pump',
          minTokenLength: sourceConfig.minTokenLength || 32,
          maxTokenLength: sourceConfig.maxTokenLength || 50,
          enableDuplicateFilter: sourceConfig.enableDuplicateFilter !== false
        }
      });

      await source.save();
      
      // Start listening to this source
      await this.startListening(source);
      
      this.logger.info(`Source ${source.name} added successfully`, {
        sourceId: source._id,
        type: source.type,
        target: source.target
      });

      // Emit to connected clients
      this.io.emit('source-added', source);
      
      return source;
    } catch (error) {
      this.logger.error('Failed to add source:', error);
      throw error;
    }
  }

  validateSourceConfig(config) {
    const required = ['name', 'type', 'target', 'telegramAccount'];
    const validTypes = ['bot', 'channel', 'group'];
    
    for (const field of required) {
      if (!config[field]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }
    
    if (!validTypes.includes(config.type)) {
      throw new Error(`Invalid type: ${config.type}. Must be one of: ${validTypes.join(', ')}`);
    }
  }

  async startListening(source) {
    try {
      const client = this.telegramPool.getClient(source.telegramAccount);
      
      // Create message handler for this source
      const handler = this.createMessageHandler(source);
      
      // Add event handler to the client
      client.addEventHandler(handler, new NewMessage({}));
      
      // Store handler reference for cleanup
      this.messageHandlers.set(source._id.toString(), handler);
      
      // Mark source as active
      this.activeSources.set(source._id.toString(), {
        source,
        client,
        handler,
        startedAt: new Date()
      });

      // Update source status
      await source.setActive();
      
      this.logger.info(`Started listening to source: ${source.name}`, {
        sourceId: source._id,
        type: source.type,
        target: source.target
      });

    } catch (error) {
      this.logger.error(`Failed to start listening to source ${source.name}:`, error);
      await source.recordError(error);
      throw error;
    }
  }

  createMessageHandler(source) {
    return async (event) => {
      try {
        if (!event.message || !event.message.text) return;
        
        const message = event.message;
        const messageText = message.text;
        
        // Check if message is from the target source
        if (!this.isMessageFromTarget(message, source)) {
          return;
        }

        // Increment message count
        await source.incrementMessages();
        
        // Extract tokens from message
        const tokens = this.extractTokens(messageText, source);
        
        if (tokens.length > 0) {
          this.logger.info(`Found ${tokens.length} tokens in message from ${source.name}`, {
            sourceId: source._id,
            tokens: tokens.slice(0, 3) // Log first 3 tokens
          });

          // Process each token
          for (const tokenAddress of tokens) {
            await this.processToken(tokenAddress, source, {
              messageText,
              messageId: message.id,
              chatId: message.chatId
            });
          }

          // Update source stats
          await source.incrementTokensFound();
          
          // Emit real-time update
          this.io.to(`source-${source._id}`).emit('tokens-found', {
            sourceId: source._id,
            tokens,
            timestamp: new Date()
          });
        }

      } catch (error) {
        this.logger.error(`Error processing message for source ${source.name}:`, error);
        await source.recordError(error);
      }
    };
  }

  isMessageFromTarget(message, source) {
    switch (source.type) {
      case 'bot':
        // Check if message is from the target bot
        return message.fromId && message.fromId.userId && 
               message.fromId.userId.toString() === source.target.replace('@', '');
      
      case 'channel':
        // Check if message is from the target channel
        return message.peerId && message.peerId.channelId && 
               message.peerId.channelId.toString() === source.target.replace('@', '');
      
      case 'group':
        // Check if message is from the target group
        return message.peerId && 
               (message.peerId.chatId?.toString() === source.target.replace('@', '') ||
                message.peerId.channelId?.toString() === source.target.replace('@', ''));
      
      default:
        return false;
    }
  }

  extractTokens(messageText, source) {
    try {
      const pattern = new RegExp(source.extractionPattern, 'gi');
      const matches = messageText.match(pattern) || [];
      
      // Filter tokens based on configuration
      const filteredTokens = matches.filter(token => {
        // Check length
        if (token.length < source.configuration.minTokenLength || 
            token.length > source.configuration.maxTokenLength) {
          return false;
        }
        
        // Check if ends with required suffix
        if (source.configuration.filterEndsWith && 
            !token.toLowerCase().endsWith(source.configuration.filterEndsWith.toLowerCase())) {
          return false;
        }
        
        return true;
      });

      // Remove duplicates if enabled
      if (source.configuration.enableDuplicateFilter) {
        return [...new Set(filteredTokens)];
      }
      
      return filteredTokens;
    } catch (error) {
      this.logger.error('Error extracting tokens:', error);
      return [];
    }
  }

  async processToken(tokenAddress, source, messageInfo) {
    try {
      // Check if token already exists
      const existingToken = await Token.findOne({ address: tokenAddress });
      
      if (existingToken) {
        this.logger.debug(`Token ${tokenAddress} already exists, skipping`);
        return;
      }

      // Create new token
      const token = new Token({
        address: tokenAddress,
        sourceId: source._id,
        discoveredMessage: {
          text: messageInfo.messageText,
          messageId: messageInfo.messageId,
          chatId: messageInfo.chatId
        },
        analysisStatus: 'pending'
      });

      await token.save();
      
      this.logger.info(`New token discovered: ${tokenAddress}`, {
        sourceId: source._id,
        sourceName: source.name
      });

      // Emit to token analyzer queue
      this.io.emit('token-discovered', {
        tokenId: token._id,
        address: tokenAddress,
        sourceId: source._id
      });

    } catch (error) {
      this.logger.error(`Error processing token ${tokenAddress}:`, error);
    }
  }

  async removeSource(sourceId) {
    try {
      const source = await Source.findById(sourceId);
      if (!source) {
        throw new Error('Source not found');
      }

      // Stop listening
      await this.stopListening(sourceId);
      
      // Mark as inactive
      source.isActive = false;
      source.status = 'paused';
      await source.save();
      
      this.logger.info(`Source ${source.name} removed`);
      
      // Emit to connected clients
      this.io.emit('source-removed', { sourceId });
      
      return source;
    } catch (error) {
      this.logger.error('Failed to remove source:', error);
      throw error;
    }
  }

  async stopListening(sourceId) {
    const activeSource = this.activeSources.get(sourceId);
    if (!activeSource) return;

    try {
      // Remove event handler
      const handler = this.messageHandlers.get(sourceId);
      if (handler) {
        activeSource.client.removeEventHandler(handler);
        this.messageHandlers.delete(sourceId);
      }

      // Remove from active sources
      this.activeSources.delete(sourceId);
      
      this.logger.info(`Stopped listening to source: ${sourceId}`);
    } catch (error) {
      this.logger.error(`Error stopping source ${sourceId}:`, error);
    }
  }

  async getAllSources() {
    return await Source.find().sort({ createdAt: -1 });
  }

  getActiveSourcesCount() {
    return this.activeSources.size;
  }

  getStats() {
    return {
      totalSources: this.activeSources.size,
      activeSources: Array.from(this.activeSources.values()).map(({ source }) => ({
        id: source._id,
        name: source.name,
        type: source.type,
        stats: source.stats
      }))
    };
  }

  async getSourceAnalytics(sourceId, timeRange = '24h') {
    try {
      const source = await Source.findById(sourceId);
      if (!source) {
        throw new Error('Source not found');
      }

      const analytics = await Token.getAnalyticsData(sourceId, timeRange);
      
      return {
        source: source.toObject(),
        analytics: analytics[0] || {
          totalTokens: 0,
          analyzedTokens: 0,
          avgRiskScore: 0,
          avgPrice: 0,
          avgHolders: 0
        }
      };
    } catch (error) {
      this.logger.error('Failed to get source analytics:', error);
      throw error;
    }
  }
}

module.exports = SourceManager;
