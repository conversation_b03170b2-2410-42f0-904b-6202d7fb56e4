const express = require('express');
const Token = require('../models/Token');
const Logger = require('../utils/logger');

function createTokensRouter(tokenAnalyzer) {
  const router = express.Router();
  const logger = new Logger();

  // Get tokens with filtering
  router.get('/', async (req, res) => {
    try {
      const filters = {
        sourceId: req.query.sourceId,
        status: req.query.status,
        timeRange: req.query.timeRange,
        limit: parseInt(req.query.limit) || 100,
        skip: parseInt(req.query.skip) || 0
      };

      // Additional filters
      if (req.query.minPrice) {
        filters.minPrice = parseFloat(req.query.minPrice);
      }
      
      if (req.query.maxPrice) {
        filters.maxPrice = parseFloat(req.query.maxPrice);
      }
      
      if (req.query.minHolders) {
        filters.minHolders = parseInt(req.query.minHolders);
      }
      
      if (req.query.maxRisk) {
        filters.maxRisk = parseInt(req.query.maxRisk);
      }

      const tokens = await tokenAnalyzer.getTokens(filters);
      
      // Apply additional filters that weren't handled in the query
      let filteredTokens = tokens;
      
      if (filters.minPrice !== undefined) {
        filteredTokens = filteredTokens.filter(token => token.price >= filters.minPrice);
      }
      
      if (filters.maxPrice !== undefined) {
        filteredTokens = filteredTokens.filter(token => token.price <= filters.maxPrice);
      }
      
      if (filters.minHolders !== undefined) {
        filteredTokens = filteredTokens.filter(token => token.holders >= filters.minHolders);
      }
      
      if (filters.maxRisk !== undefined) {
        filteredTokens = filteredTokens.filter(token => token.riskScore <= filters.maxRisk);
      }

      res.json({
        success: true,
        data: filteredTokens,
        count: filteredTokens.length,
        filters: filters
      });
    } catch (error) {
      logger.error('Error fetching tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch tokens'
      });
    }
  });

  // Get token by ID
  router.get('/:id', async (req, res) => {
    try {
      const token = await Token.findById(req.params.id).populate('sourceId');
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      res.json({
        success: true,
        data: token
      });
    } catch (error) {
      logger.error('Error fetching token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch token'
      });
    }
  });

  // Get token by address
  router.get('/address/:address', async (req, res) => {
    try {
      const token = await Token.findOne({ address: req.params.address }).populate('sourceId');
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      res.json({
        success: true,
        data: token
      });
    } catch (error) {
      logger.error('Error fetching token by address:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch token'
      });
    }
  });

  // Update token (manual override)
  router.put('/:id', async (req, res) => {
    try {
      const token = await Token.findById(req.params.id);
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      // Allow updating certain fields manually
      const allowedUpdates = [
        'symbol', 'name', 'price', 'marketCap', 'volume24h', 
        'holders', 'riskScore', 'tags', 'category', 'metadata',
        'isVerified', 'isFlagged', 'flagReason'
      ];
      
      const updates = {};
      allowedUpdates.forEach(field => {
        if (req.body[field] !== undefined) {
          updates[field] = req.body[field];
        }
      });

      Object.assign(token, updates);
      await token.save();

      res.json({
        success: true,
        data: token,
        message: 'Token updated successfully'
      });
    } catch (error) {
      logger.error('Error updating token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update token'
      });
    }
  });

  // Flag/unflag token
  router.patch('/:id/flag', async (req, res) => {
    try {
      const token = await Token.findById(req.params.id);
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      token.isFlagged = !token.isFlagged;
      token.flagReason = req.body.reason || '';
      
      await token.save();

      res.json({
        success: true,
        data: token,
        message: `Token ${token.isFlagged ? 'flagged' : 'unflagged'} successfully`
      });
    } catch (error) {
      logger.error('Error flagging token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to flag token'
      });
    }
  });

  // Requeue token for analysis
  router.post('/:id/reanalyze', async (req, res) => {
    try {
      const token = await Token.findById(req.params.id);
      
      if (!token) {
        return res.status(404).json({
          success: false,
          error: 'Token not found'
        });
      }

      // Reset analysis status
      token.analysisStatus = 'pending';
      token.analysisAttempts = 0;
      token.analysisError = undefined;
      await token.save();

      // Queue for analysis
      await tokenAnalyzer.queueTokenForAnalysis(token._id);

      res.json({
        success: true,
        data: token,
        message: 'Token queued for re-analysis'
      });
    } catch (error) {
      logger.error('Error requeuing token:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to requeue token'
      });
    }
  });

  // Get token statistics
  router.get('/stats/summary', async (req, res) => {
    try {
      const timeRange = req.query.timeRange || '24h';
      const sourceId = req.query.sourceId;
      
      const timeRanges = {
        '1h': 1 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      
      const since = new Date(Date.now() - timeRanges[timeRange]);
      const matchQuery = { discoveredAt: { $gte: since } };
      
      if (sourceId) {
        matchQuery.sourceId = sourceId;
      }

      const stats = await Token.aggregate([
        { $match: matchQuery },
        {
          $group: {
            _id: null,
            totalTokens: { $sum: 1 },
            analyzedTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'completed'] }, 1, 0] }
            },
            pendingTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'pending'] }, 1, 0] }
            },
            failedTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'failed'] }, 1, 0] }
            },
            avgPrice: { $avg: '$price' },
            avgHolders: { $avg: '$holders' },
            avgRiskScore: { $avg: '$riskScore' },
            flaggedTokens: {
              $sum: { $cond: ['$isFlagged', 1, 0] }
            }
          }
        }
      ]);

      const result = stats[0] || {
        totalTokens: 0,
        analyzedTokens: 0,
        pendingTokens: 0,
        failedTokens: 0,
        avgPrice: 0,
        avgHolders: 0,
        avgRiskScore: 0,
        flaggedTokens: 0
      };

      // Add success rate
      result.successRate = result.totalTokens > 0 
        ? Math.round((result.analyzedTokens / result.totalTokens) * 100) 
        : 0;

      res.json({
        success: true,
        data: result,
        timeRange,
        sourceId
      });
    } catch (error) {
      logger.error('Error fetching token stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch token statistics'
      });
    }
  });

  // Get tokens by category
  router.get('/category/:category', async (req, res) => {
    try {
      const category = req.params.category;
      const limit = parseInt(req.query.limit) || 50;
      const skip = parseInt(req.query.skip) || 0;

      const tokens = await Token.find({ category })
        .populate('sourceId')
        .sort({ discoveredAt: -1 })
        .limit(limit)
        .skip(skip);

      res.json({
        success: true,
        data: tokens,
        count: tokens.length,
        category
      });
    } catch (error) {
      logger.error('Error fetching tokens by category:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch tokens by category'
      });
    }
  });

  // Search tokens
  router.get('/search/:query', async (req, res) => {
    try {
      const query = req.params.query;
      const limit = parseInt(req.query.limit) || 50;

      const tokens = await Token.find({
        $or: [
          { address: { $regex: query, $options: 'i' } },
          { symbol: { $regex: query, $options: 'i' } },
          { name: { $regex: query, $options: 'i' } },
          { tags: { $in: [new RegExp(query, 'i')] } }
        ]
      })
        .populate('sourceId')
        .sort({ discoveredAt: -1 })
        .limit(limit);

      res.json({
        success: true,
        data: tokens,
        count: tokens.length,
        query
      });
    } catch (error) {
      logger.error('Error searching tokens:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to search tokens'
      });
    }
  });

  return router;
}

module.exports = createTokensRouter;
