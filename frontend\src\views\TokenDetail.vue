<template>
  <div class="token-detail-page">
    <div class="page-header">
      <button @click="$router.back()" class="back-btn">← Back</button>
      <h1>{{ token.symbol || token.name || 'Token Details' }}</h1>
    </div>

    <div v-if="token.id" class="token-info">
      <div class="basic-info-card">
        <h3>Basic Information</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>Address:</label>
            <span class="address">{{ token.address }}</span>
          </div>
          <div class="info-item">
            <label>Symbol:</label>
            <span>{{ token.symbol || '-' }}</span>
          </div>
          <div class="info-item">
            <label>Name:</label>
            <span>{{ token.name || '-' }}</span>
          </div>
          <div class="info-item">
            <label>Discovered:</label>
            <span>{{ formatDate(token.discovered_at) }}</span>
          </div>
        </div>
      </div>

      <div class="financial-info-card">
        <h3>Financial Data</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>Price:</label>
            <span>{{ formatPrice(token.price) }}</span>
          </div>
          <div class="info-item">
            <label>Market Cap:</label>
            <span>{{ token.market_cap || '-' }}</span>
          </div>
          <div class="info-item">
            <label>Volume 24h:</label>
            <span>{{ token.volume_24h || '-' }}</span>
          </div>
          <div class="info-item">
            <label>Holders:</label>
            <span>{{ token.holders || '-' }}</span>
          </div>
        </div>
      </div>

      <div class="risk-info-card">
        <h3>Risk Analysis</h3>
        <div class="risk-score">
          <span class="label">Risk Score:</span>
          <span :class="['score', getRiskClass(token.risk_score)]">
            {{ token.risk_score || '-' }}/10
          </span>
        </div>
        <div class="risk-factors" v-if="token.risk_factors && token.risk_factors.length">
          <h4>Risk Factors:</h4>
          <ul>
            <li v-for="factor in token.risk_factors" :key="factor.factor">
              <span :class="['severity', factor.severity]">{{ factor.severity }}</span>
              {{ factor.description }}
            </li>
          </ul>
        </div>
      </div>

      <div class="analysis-info-card">
        <h3>Analysis Status</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>Status:</label>
            <span :class="['status', token.analysis_status]">{{ token.analysis_status }}</span>
          </div>
          <div class="info-item">
            <label>Attempts:</label>
            <span>{{ token.analysis_attempts || 0 }}</span>
          </div>
          <div class="info-item">
            <label>Last Attempt:</label>
            <span>{{ formatDate(token.last_analysis_attempt) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="loading">
      <p>Loading token details...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TokenDetail',
  data() {
    return {
      token: {}
    }
  },
  async mounted() {
    await this.loadToken()
  },
  methods: {
    async loadToken() {
      try {
        const id = this.$route.params.id
        const response = await fetch(`/api/tokens/${id}`)
        this.token = await response.json()
      } catch (error) {
        console.error('Failed to load token:', error)
      }
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString()
    },
    formatPrice(price) {
      if (!price || price === 0) return '-'
      return '$' + parseFloat(price).toFixed(6)
    },
    getRiskClass(score) {
      if (!score) return 'unknown'
      if (score <= 3) return 'low'
      if (score <= 6) return 'medium'
      return 'high'
    }
  }
}
</script>

<style scoped>
.token-detail-page {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.back-btn {
  background: #f5f5f5;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
}

.token-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.basic-info-card,
.financial-info-card,
.risk-info-card,
.analysis-info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-grid {
  display: grid;
  gap: 15px;
}

.info-item {
  display: flex;
  justify-content: space-between;
}

.info-item label {
  font-weight: bold;
  color: #666;
}

.address {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.risk-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.score {
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 18px;
}

.score.low { background: #e8f5e8; color: #2e7d32; }
.score.medium { background: #fff3e0; color: #ef6c00; }
.score.high { background: #ffebee; color: #c62828; }
.score.unknown { background: #f5f5f5; color: #666; }

.risk-factors ul {
  list-style: none;
  padding: 0;
}

.risk-factors li {
  margin-bottom: 10px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.severity {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  margin-right: 8px;
}

.severity.low { background: #e8f5e8; color: #2e7d32; }
.severity.medium { background: #fff3e0; color: #ef6c00; }
.severity.high { background: #ffebee; color: #c62828; }
.severity.critical { background: #000; color: #fff; }

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status.pending { background: #fff3e0; color: #ef6c00; }
.status.completed { background: #e8f5e8; color: #2e7d32; }
.status.failed { background: #ffebee; color: #c62828; }

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}

@media (max-width: 768px) {
  .token-info {
    grid-template-columns: 1fr;
  }
}
</style>
