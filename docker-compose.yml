version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: demure-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: demure_root_password
      MYSQL_DATABASE: demure_web_app
      MYSQL_USER: demure_user
      MYSQL_PASSWORD: demure_password
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - demure-network
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7.2-alpine
    container_name: demure-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - demure-network

volumes:
  mysql_data:
  redis_data:

networks:
  demure-network:
    driver: bridge
