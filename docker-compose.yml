version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: demure-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_DATABASE: demure-web-app
    volumes:
      - mongodb_data:/data/db
    networks:
      - demure-network

  redis:
    image: redis:7.2-alpine
    container_name: demure-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - demure-network

volumes:
  mongodb_data:
  redis_data:

networks:
  demure-network:
    driver: bridge
