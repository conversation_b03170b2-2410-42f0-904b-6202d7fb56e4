// Simple Redis mock for development without Redis server
class RedisMock {
  constructor() {
    this.data = new Map();
    this.connected = true;
  }

  async connect() {
    this.connected = true;
    return this;
  }

  async disconnect() {
    this.connected = false;
  }

  async set(key, value, options = {}) {
    this.data.set(key, value);
    if (options.EX) {
      // Simulate expiration (simplified)
      setTimeout(() => {
        this.data.delete(key);
      }, options.EX * 1000);
    }
    return 'OK';
  }

  async get(key) {
    return this.data.get(key) || null;
  }

  async del(key) {
    return this.data.delete(key) ? 1 : 0;
  }

  async exists(key) {
    return this.data.has(key) ? 1 : 0;
  }

  async keys(pattern) {
    const keys = Array.from(this.data.keys());
    if (pattern === '*') return keys;
    
    // Simple pattern matching
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return keys.filter(key => regex.test(key));
  }

  async flushall() {
    this.data.clear();
    return 'OK';
  }

  isReady() {
    return this.connected;
  }

  on(event, callback) {
    // Mock event handling
    if (event === 'connect' && this.connected) {
      setTimeout(callback, 0);
    }
  }
}

module.exports = RedisMock;
