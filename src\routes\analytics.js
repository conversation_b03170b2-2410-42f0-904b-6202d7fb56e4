const express = require('express');
const ExcelJS = require('exceljs');
const Token = require('../models/Token');
const Source = require('../models/Source');
const Logger = require('../utils/logger');

function createAnalyticsRouter(tokenAnalyzer) {
  const router = express.Router();
  const logger = new Logger();

  // Generate Excel report for a source
  router.get('/excel/:sourceId', async (req, res) => {
    try {
      const sourceId = req.params.sourceId;
      const timeRange = req.query.timeRange || '24h';
      const limit = parseInt(req.query.limit) || 10000;

      // Get source info
      const source = await Source.findById(sourceId);
      if (!source) {
        return res.status(404).json({
          success: false,
          error: 'Source not found'
        });
      }

      // Get tokens for this source
      const tokens = await tokenAnalyzer.getTokens({
        sourceId,
        timeRange,
        limit
      });

      // Create Excel workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(`${source.name} - Token Analysis`);

      // Set up headers with T-MC tracking
      const headers = [
        'Token Address',
        'Symbol',
        'Name',
        'Price',
        'MC (Initial)',
        'T-MC (Initial)',
        'T-MC 1H',
        'T-MC 5H',
        'T-MC 24H',
        'Profit 1H (%)',
        'Profit 5H (%)',
        'Profit 24H (%)',
        '24h Volume',
        'Holders',
        'Liquidity',
        'FDV',
        'Risk Score',
        'Category',
        'Status',
        'Discovered At',
        'Age (Hours)',
        'Tags',
        'Is Flagged',
        'Flag Reason'
      ];

      const headerRow = worksheet.addRow(headers);

      // Style headers
      headerRow.eachCell((cell) => {
        cell.font = { bold: true, color: { argb: 'FFFFFF' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '366092' }
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // Add data rows with T-MC tracking
      tokens.forEach((token, index) => {
        // Calculate profit percentages
        const calculateProfit = (initialMC, tMcValue) => {
          if (!initialMC || !tMcValue || tMcValue === 'N/A') return 'N/A';

          const initial = parseFloat(initialMC.replace(/[^0-9.-]+/g, ''));
          const current = parseFloat(tMcValue.replace(/[^0-9.-]+/g, ''));

          if (isNaN(initial) || isNaN(current) || initial === 0) return 'N/A';

          return ((current - initial) / initial * 100).toFixed(2);
        };

        const profit1h = calculateProfit(token.marketCap, token.tMc1h);
        const profit5h = calculateProfit(token.marketCap, token.tMc5h);
        const profit24h = calculateProfit(token.marketCap, token.tMc24h);

        const row = worksheet.addRow([
          token.address,
          token.symbol || 'N/A',
          token.name || 'N/A',
          token.price || 0,
          token.marketCap || 'N/A',
          token.topMarketCap || 'N/A',
          token.tMc1h || 'N/A',
          token.tMc5h || 'N/A',
          token.tMc24h || 'N/A',
          profit1h,
          profit5h,
          profit24h,
          token.volume24h || 'N/A',
          token.holders || 0,
          token.liquidity || 'N/A',
          token.fdv || 'N/A',
          token.riskScore || 5,
          token.category || 'unknown',
          token.analysisStatus,
          token.discoveredAt.toISOString(),
          token.ageInHours || 0,
          token.tags.join(', ') || '',
          token.isFlagged ? 'Yes' : 'No',
          token.flagReason || ''
        ]);

        // Conditional formatting based on risk score
        const riskCell = row.getCell(17); // Risk Score column (updated position)
        if (token.riskScore >= 8) {
          riskCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFF0000' } // Red for high risk
          };
        } else if (token.riskScore >= 6) {
          riskCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFFFFF00' } // Yellow for medium risk
          };
        } else {
          riskCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FF00FF00' } // Green for low risk
          };
        }

        // Highlight profit cells based on performance
        const highlightProfitCell = (cellIndex, profitValue) => {
          if (profitValue === 'N/A') return;

          const profit = parseFloat(profitValue);
          const cell = row.getCell(cellIndex);

          if (profit > 100) { // >100% profit
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FF00FF00' } // Bright green
            };
            cell.font = { bold: true, color: { argb: 'FFFFFF' } };
          } else if (profit > 50) { // >50% profit
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FF90EE90' } // Light green
            };
          } else if (profit > 0) { // Positive profit
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFF0FFF0' } // Very light green
            };
          } else if (profit < -50) { // >50% loss
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFFF0000' } // Red
            };
            cell.font = { color: { argb: 'FFFFFF' } };
          } else if (profit < 0) { // Negative profit
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFFFCCCC' } // Light red
            };
          }
        };

        // Apply profit highlighting
        highlightProfitCell(10, profit1h);   // Profit 1H
        highlightProfitCell(11, profit5h);   // Profit 5H
        highlightProfitCell(12, profit24h);  // Profit 24H

        // Highlight flagged tokens
        if (token.isFlagged) {
          row.eachCell((cell) => {
            cell.font = { color: { argb: 'FFFF0000' } };
          });
        }
      });

      // Auto-fit columns
      worksheet.columns.forEach((column) => {
        let maxLength = 0;
        column.eachCell({ includeEmpty: true }, (cell) => {
          const columnLength = cell.value ? cell.value.toString().length : 10;
          if (columnLength > maxLength) {
            maxLength = columnLength;
          }
        });
        column.width = Math.min(maxLength + 2, 50);
      });

      // Add summary sheet
      const summarySheet = workbook.addWorksheet('Summary');

      // Calculate T-MC statistics
      const tokensWithTMc1h = tokens.filter(t => t.tMc1h && t.tMc1h !== 'N/A').length;
      const tokensWithTMc5h = tokens.filter(t => t.tMc5h && t.tMc5h !== 'N/A').length;
      const tokensWithTMc24h = tokens.filter(t => t.tMc24h && t.tMc24h !== 'N/A').length;

      const summary = [
        ['Source Name', source.name],
        ['Source Type', source.type],
        ['Target', source.target],
        ['Report Generated', new Date().toISOString()],
        ['Time Range', timeRange],
        ['', ''], // Empty row
        ['=== TOKEN ANALYSIS ===', ''],
        ['Total Tokens', tokens.length],
        ['Analyzed Tokens', tokens.filter(t => t.analysisStatus === 'completed').length],
        ['Pending Analysis', tokens.filter(t => t.analysisStatus === 'pending').length],
        ['Failed Analysis', tokens.filter(t => t.analysisStatus === 'failed').length],
        ['Flagged Tokens', tokens.filter(t => t.isFlagged).length],
        ['', ''], // Empty row
        ['=== T-MC TRACKING ===', ''],
        ['Tokens with T-MC 1H', tokensWithTMc1h],
        ['Tokens with T-MC 5H', tokensWithTMc5h],
        ['Tokens with T-MC 24H', tokensWithTMc24h],
        ['T-MC 1H Success Rate', tokens.length > 0 ? `${((tokensWithTMc1h / tokens.length) * 100).toFixed(1)}%` : '0%'],
        ['T-MC 5H Success Rate', tokens.length > 0 ? `${((tokensWithTMc5h / tokens.length) * 100).toFixed(1)}%` : '0%'],
        ['T-MC 24H Success Rate', tokens.length > 0 ? `${((tokensWithTMc24h / tokens.length) * 100).toFixed(1)}%` : '0%'],
        ['', ''], // Empty row
        ['=== AVERAGES ===', ''],
        ['Average Risk Score', tokens.length > 0 ? (tokens.reduce((sum, t) => sum + (t.riskScore || 5), 0) / tokens.length).toFixed(2) : 0],
        ['Average Price', tokens.length > 0 ? (tokens.reduce((sum, t) => sum + (t.price || 0), 0) / tokens.length).toFixed(8) : 0],
        ['Average Holders', tokens.length > 0 ? Math.round(tokens.reduce((sum, t) => sum + (t.holders || 0), 0) / tokens.length) : 0]
      ];

      summary.forEach(([key, value]) => {
        const row = summarySheet.addRow([key, value]);
        row.getCell(1).font = { bold: true };
      });

      summarySheet.columns = [
        { width: 20 },
        { width: 30 }
      ];

      // Set response headers
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="tokens-${source.name}-${new Date().toISOString().split('T')[0]}.xlsx"`
      );

      // Write to response
      await workbook.xlsx.write(res);
      res.end();

      logger.info(`Excel report generated for source ${source.name}`, {
        sourceId,
        tokenCount: tokens.length,
        timeRange
      });

    } catch (error) {
      logger.error('Error generating Excel report:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate Excel report'
      });
    }
  });

  // Get dashboard analytics for a source
  router.get('/dashboard/:sourceId', async (req, res) => {
    try {
      const sourceId = req.params.sourceId;
      const timeRange = req.query.timeRange || '24h';

      const analytics = await tokenAnalyzer.sourceManager.getSourceAnalytics(sourceId, timeRange);

      // Get additional metrics
      const tokens = await tokenAnalyzer.getTokens({ sourceId, timeRange, limit: 1000 });

      // Calculate performance metrics
      const performanceMetrics = {
        totalTokens: tokens.length,
        analyzedTokens: tokens.filter(t => t.analysisStatus === 'completed').length,
        pendingTokens: tokens.filter(t => t.analysisStatus === 'pending').length,
        failedTokens: tokens.filter(t => t.analysisStatus === 'failed').length,
        flaggedTokens: tokens.filter(t => t.isFlagged).length,

        // Risk distribution
        riskDistribution: {
          low: tokens.filter(t => t.riskScore <= 3).length,
          medium: tokens.filter(t => t.riskScore > 3 && t.riskScore <= 7).length,
          high: tokens.filter(t => t.riskScore > 7).length
        },

        // Category distribution
        categoryDistribution: tokens.reduce((acc, token) => {
          const category = token.category || 'unknown';
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        }, {}),

        // Price ranges
        priceRanges: {
          micro: tokens.filter(t => t.price > 0 && t.price < 0.000001).length,
          low: tokens.filter(t => t.price >= 0.000001 && t.price < 0.001).length,
          medium: tokens.filter(t => t.price >= 0.001 && t.price < 0.1).length,
          high: tokens.filter(t => t.price >= 0.1).length
        },

        // Recent activity (last hour)
        recentActivity: {
          discovered: tokens.filter(t =>
            new Date() - new Date(t.discoveredAt) < 60 * 60 * 1000
          ).length,
          analyzed: tokens.filter(t =>
            t.analysisStatus === 'completed' &&
            new Date() - new Date(t.updatedAt) < 60 * 60 * 1000
          ).length
        }
      };

      res.json({
        success: true,
        data: {
          ...analytics,
          performanceMetrics,
          timeRange
        }
      });

    } catch (error) {
      logger.error('Error fetching dashboard analytics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dashboard analytics'
      });
    }
  });

  // Get global analytics (all sources)
  router.get('/global', async (req, res) => {
    try {
      const timeRange = req.query.timeRange || '24h';

      // Get all sources
      const sources = await Source.find({ isActive: true });

      // Get global token stats
      const globalStats = await Token.aggregate([
        {
          $group: {
            _id: null,
            totalTokens: { $sum: 1 },
            analyzedTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'completed'] }, 1, 0] }
            },
            pendingTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'pending'] }, 1, 0] }
            },
            failedTokens: {
              $sum: { $cond: [{ $eq: ['$analysisStatus', 'failed'] }, 1, 0] }
            },
            avgRiskScore: { $avg: '$riskScore' },
            avgPrice: { $avg: '$price' },
            avgHolders: { $avg: '$holders' }
          }
        }
      ]);

      // Get source performance
      const sourcePerformance = await Promise.all(
        sources.map(async (source) => {
          const tokens = await Token.countDocuments({ sourceId: source._id });
          const analyzed = await Token.countDocuments({
            sourceId: source._id,
            analysisStatus: 'completed'
          });

          return {
            id: source._id,
            name: source.name,
            type: source.type,
            totalTokens: tokens,
            analyzedTokens: analyzed,
            successRate: tokens > 0 ? Math.round((analyzed / tokens) * 100) : 0,
            stats: source.stats
          };
        })
      );

      res.json({
        success: true,
        data: {
          globalStats: globalStats[0] || {
            totalTokens: 0,
            analyzedTokens: 0,
            pendingTokens: 0,
            failedTokens: 0,
            avgRiskScore: 0,
            avgPrice: 0,
            avgHolders: 0
          },
          sourcePerformance,
          totalSources: sources.length,
          activeSources: sources.filter(s => s.status === 'active').length,
          timeRange
        }
      });

    } catch (error) {
      logger.error('Error fetching global analytics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch global analytics'
      });
    }
  });

  // Export all data as Excel
  router.get('/excel/global', async (req, res) => {
    try {
      const timeRange = req.query.timeRange || '24h';
      const limit = parseInt(req.query.limit) || 10000;

      // Get all tokens
      const tokens = await tokenAnalyzer.getTokens({ timeRange, limit });

      // Create workbook with multiple sheets
      const workbook = new ExcelJS.Workbook();

      // Group tokens by source
      const tokensBySource = tokens.reduce((acc, token) => {
        const sourceName = token.sourceId?.name || 'Unknown Source';
        if (!acc[sourceName]) acc[sourceName] = [];
        acc[sourceName].push(token);
        return acc;
      }, {});

      // Create sheet for each source
      Object.entries(tokensBySource).forEach(([sourceName, sourceTokens]) => {
        const worksheet = workbook.addWorksheet(sourceName.substring(0, 31)); // Excel sheet name limit

        // Add headers and data (similar to single source export)
        const headers = [
          'Token Address', 'Symbol', 'Name', 'Price', 'Market Cap',
          '24h Volume', 'Holders', 'Risk Score', 'Category', 'Status',
          'Discovered At', 'Age (Hours)', 'Is Flagged'
        ];

        worksheet.addRow(headers);

        sourceTokens.forEach(token => {
          worksheet.addRow([
            token.address,
            token.symbol || 'N/A',
            token.name || 'N/A',
            token.price || 0,
            token.marketCap || 'N/A',
            token.volume24h || 'N/A',
            token.holders || 0,
            token.riskScore || 5,
            token.category || 'unknown',
            token.analysisStatus,
            token.discoveredAt.toISOString(),
            token.ageInHours || 0,
            token.isFlagged ? 'Yes' : 'No'
          ]);
        });
      });

      // Set response headers
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="all-tokens-${new Date().toISOString().split('T')[0]}.xlsx"`
      );

      await workbook.xlsx.write(res);
      res.end();

    } catch (error) {
      logger.error('Error generating global Excel report:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate global Excel report'
      });
    }
  });

  // Get T-MC rescan statistics
  router.get('/rescan-stats', async (req, res) => {
    try {
      // This would need to be passed from the main server
      // For now, we'll calculate from the database directly
      const stats = await Token.aggregate([
        {
          $group: {
            _id: null,
            totalTokens: { $sum: 1 },
            completed1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'completed'] }, 1, 0] }
            },
            completed5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'completed'] }, 1, 0] }
            },
            completed24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'completed'] }, 1, 0] }
            },
            pending1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'pending'] }, 1, 0] }
            },
            pending5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'pending'] }, 1, 0] }
            },
            pending24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'pending'] }, 1, 0] }
            },
            failed1h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.1h.status', 'failed'] }, 1, 0] }
            },
            failed5h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.5h.status', 'failed'] }, 1, 0] }
            },
            failed24h: {
              $sum: { $cond: [{ $eq: ['$rescanStatus.24h.status', 'failed'] }, 1, 0] }
            }
          }
        }
      ]);

      const result = stats[0] || {
        totalTokens: 0,
        completed1h: 0, completed5h: 0, completed24h: 0,
        pending1h: 0, pending5h: 0, pending24h: 0,
        failed1h: 0, failed5h: 0, failed24h: 0
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('Error fetching rescan stats:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch rescan statistics'
      });
    }
  });

  return router;
}

module.exports = createAnalyticsRouter;
