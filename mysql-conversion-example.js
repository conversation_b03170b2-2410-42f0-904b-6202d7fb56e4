// Example: Converting database-service.js from MongoDB to MySQL
// This shows the practical implementation of the MySQL migration

const { Sequelize } = require('sequelize');
const Logger = require('../utils/logger');

class DatabaseService {
  constructor() {
    this.logger = new Logger();
    this.sequelize = null;
  }

  async connect() {
    try {
      // MySQL connection configuration
      this.sequelize = new Sequelize({
        host: process.env.MYSQL_HOST || 'localhost',
        port: process.env.MYSQL_PORT || 3306,
        database: process.env.MYSQL_DATABASE || 'demure_web_app',
        username: process.env.MYSQL_USERNAME || 'root',
        password: process.env.MYSQL_PASSWORD || '',
        dialect: 'mysql',
        
        // Connection pool settings
        pool: {
          max: 10,
          min: 0,
          acquire: 30000,
          idle: 10000
        },
        
        // Logging
        logging: (msg) => this.logger.debug(msg),
        
        // Additional options
        define: {
          timestamps: true,
          underscored: true, // Use snake_case for column names
          freezeTableName: true // Don't pluralize table names
        }
      });

      // Test the connection
      await this.sequelize.authenticate();
      this.logger.info('Connected to MySQL successfully');

      // Sync models (create tables if they don't exist)
      await this.sequelize.sync({ alter: false });
      this.logger.info('Database models synchronized');

      return this.sequelize;
    } catch (error) {
      this.logger.error('Failed to connect to MySQL:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.sequelize) {
        await this.sequelize.close();
        this.logger.info('Disconnected from MySQL');
      }
    } catch (error) {
      this.logger.error('Error disconnecting from MySQL:', error);
      throw error;
    }
  }

  isConnected() {
    return this.sequelize && this.sequelize.authenticate()
      .then(() => true)
      .catch(() => false);
  }

  async getConnectionStatus() {
    if (!this.sequelize) {
      return { state: 'disconnected' };
    }

    try {
      await this.sequelize.authenticate();
      return {
        state: 'connected',
        host: this.sequelize.config.host,
        port: this.sequelize.config.port,
        database: this.sequelize.config.database
      };
    } catch (error) {
      return { state: 'disconnected', error: error.message };
    }
  }
}

module.exports = DatabaseService;

// Example: Converting Source model from Mongoose to Sequelize
const { DataTypes } = require('sequelize');

function defineSourceModel(sequelize) {
  const Source = sequelize.define('Source', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100]
      }
    },
    type: {
      type: DataTypes.ENUM('bot', 'channel', 'group'),
      allowNull: false
    },
    target: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    telegram_account: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    extraction_pattern: {
      type: DataTypes.STRING(255),
      defaultValue: '[A-Za-z0-9]{32,}pump'
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    
    // Configuration stored as JSON
    configuration: {
      type: DataTypes.JSON,
      defaultValue: {
        filterEndsWith: 'pump',
        minTokenLength: 32,
        maxTokenLength: 50,
        enableDuplicateFilter: true
      }
    },
    
    // Statistics
    total_messages: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    tokens_found: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    tokens_analyzed: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    last_message_at: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    tableName: 'sources',
    indexes: [
      { fields: ['type'] },
      { fields: ['is_active'] },
      { fields: ['telegram_account'] }
    ]
  });

  // Instance methods (equivalent to Mongoose methods)
  Source.prototype.incrementMessages = async function() {
    this.total_messages += 1;
    this.last_message_at = new Date();
    return await this.save();
  };

  Source.prototype.incrementTokensFound = async function() {
    this.tokens_found += 1;
    return await this.save();
  };

  Source.prototype.incrementTokensAnalyzed = async function() {
    this.tokens_analyzed += 1;
    return await this.save();
  };

  return Source;
}

// Example: Converting Token model from Mongoose to Sequelize
function defineTokenModel(sequelize) {
  const Token = sequelize.define('Token', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    address: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      }
    },
    source_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'sources',
        key: 'id'
      }
    },
    
    // Basic token information
    symbol: {
      type: DataTypes.STRING(20),
      allowNull: true
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    
    // Financial data
    price: {
      type: DataTypes.DECIMAL(20, 10),
      defaultValue: 0
    },
    market_cap: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    top_market_cap: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    volume_24h: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    holders: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    liquidity: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    fdv: {
      type: DataTypes.STRING(50),
      allowNull: true
    },
    
    // Risk analysis
    risk_score: {
      type: DataTypes.DECIMAL(3, 1),
      defaultValue: 0
    },
    risk_factors: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    is_flagged: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    
    // Analysis status
    analysis_status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'timeout'),
      defaultValue: 'pending'
    },
    analysis_attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    last_analysis_attempt: {
      type: DataTypes.DATE,
      allowNull: true
    },
    analysis_error: {
      type: DataTypes.JSON,
      allowNull: true
    },
    
    // Soul Scanner response
    soul_scanner_response: {
      type: DataTypes.JSON,
      allowNull: true
    },
    
    // Discovery information
    discovered_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    discovered_message: {
      type: DataTypes.JSON,
      allowNull: true
    },
    
    // T-MC tracking
    rescan_status: {
      type: DataTypes.JSON,
      defaultValue: {}
    }
  }, {
    tableName: 'tokens',
    indexes: [
      { fields: ['address'] },
      { fields: ['source_id'] },
      { fields: ['analysis_status'] },
      { fields: ['discovered_at'] },
      { fields: ['risk_score'] }
    ]
  });

  // Instance methods
  Token.prototype.incrementAnalysisAttempts = async function() {
    this.analysis_attempts += 1;
    this.last_analysis_attempt = new Date();
    return await this.save();
  };

  Token.prototype.startRescan = async function(interval) {
    if (!this.rescan_status) this.rescan_status = {};
    this.rescan_status[interval] = {
      status: 'processing',
      attempts: (this.rescan_status[interval]?.attempts || 0) + 1,
      startedAt: new Date()
    };
    return await this.save();
  };

  Token.prototype.completeRescan = async function(interval, tMcValue) {
    if (!this.rescan_status) this.rescan_status = {};
    this.rescan_status[interval] = {
      ...this.rescan_status[interval],
      status: 'completed',
      tMcValue,
      completedAt: new Date()
    };
    return await this.save();
  };

  return Token;
}

// Example: Setting up model associations
function setupAssociations(sequelize) {
  const Source = sequelize.models.Source;
  const Token = sequelize.models.Token;

  // Define relationships
  Source.hasMany(Token, { foreignKey: 'source_id', as: 'tokens' });
  Token.belongsTo(Source, { foreignKey: 'source_id', as: 'source' });
}

module.exports = {
  DatabaseService,
  defineSourceModel,
  defineTokenModel,
  setupAssociations
};
