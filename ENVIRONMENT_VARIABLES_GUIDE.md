# Environment Variables Guide for Demure Web App

## Complete Environment Variables Reference

### 🔧 Server Configuration

#### PORT (Optional)
- **Default**: `3002`
- **Description**: Port number for the backend server
- **Example**: `PORT=3002`
- **How to get**: Choose any available port (3000-9999)

#### NODE_ENV (Optional)
- **Default**: `development`
- **Description**: Application environment mode
- **Options**: `development`, `production`, `test`
- **Example**: `NODE_ENV=development`

#### CORS_ORIGIN (Optional)
- **Default**: `http://localhost:3000`
- **Description**: Allowed origin for CORS requests (frontend URL)
- **Example**: `CORS_ORIGIN=http://localhost:3000`
- **Production**: Set to your domain (e.g., `https://yourdomain.com`)

---

### 🗄️ Database Configuration

#### MONGODB_URI (Required - Current)
- **Default**: `mongodb://localhost:27017/demure-web-app`
- **Description**: MongoDB connection string
- **Example**: `MONGODB_URI=mongodb://localhost:27017/demure-web-app`
- **How to get**: 
  - Local: Install MongoDB locally
  - Cloud: Use MongoDB Atlas (free tier available)
  - Format: `********************************:port/database`

#### MYSQL Configuration (For MySQL Migration)
```env
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=demure_web_app
MYSQL_USERNAME=your_username
MYSQL_PASSWORD=your_password
MYSQL_DIALECT=mysql
```

#### REDIS_URL (Required)
- **Default**: `redis://localhost:6379`
- **Description**: Redis connection for queue management
- **Example**: `REDIS_URL=redis://localhost:6379`
- **How to get**:
  - Local: Install Redis locally
  - Cloud: Use Redis Cloud, AWS ElastiCache, etc.
  - Format: `redis://username:password@host:port`

---

### 📱 Telegram API Configuration (Required)

#### Multiple Telegram Accounts
You need at least one Telegram account, but can add multiple for load balancing:

#### Account 1 (Required)
```env
TELEGRAM_API_ID_1=your_api_id_1
TELEGRAM_API_HASH_1=your_api_hash_1
TELEGRAM_PHONE_1=+**********
TELEGRAM_SESSION_1=your_session_string_1
```

#### Account 2 (Optional)
```env
TELEGRAM_API_ID_2=your_api_id_2
TELEGRAM_API_HASH_2=your_api_hash_2
TELEGRAM_PHONE_2=+**********
TELEGRAM_SESSION_2=your_session_string_2
```

#### How to get Telegram credentials:
1. **Go to**: https://my.telegram.org/apps
2. **Login** with your phone number
3. **Create new application**:
   - App title: "Demure Web App"
   - Short name: "demure-app"
   - Platform: "Desktop"
4. **Copy** API ID and API Hash
5. **Session String**: Generated automatically when you first run the app

---

### 🤖 Soul Scanner Bot Configuration

#### SOUL_SCANNER_BOT_USERNAME (Required)
- **Default**: `@soul_scanner_bot`
- **Description**: Username of the Soul Scanner Telegram bot
- **Example**: `SOUL_SCANNER_BOT_USERNAME=@soul_scanner_bot`
- **How to get**: This is the actual bot username on Telegram

#### SOUL_SCANNER_TIMEOUT (Optional)
- **Default**: `30000` (30 seconds)
- **Description**: Timeout for Soul Scanner bot responses in milliseconds
- **Example**: `SOUL_SCANNER_TIMEOUT=30000`

---

### ⚡ Rate Limiting Configuration

#### REQUESTS_PER_MINUTE (Optional)
- **Default**: `30`
- **Description**: Maximum API requests per minute per client
- **Example**: `REQUESTS_PER_MINUTE=30`
- **Recommendation**: Start with 30, adjust based on usage

#### SOUL_SCANNER_DELAY (Optional)
- **Default**: `2000` (2 seconds)
- **Description**: Delay between Soul Scanner bot requests in milliseconds
- **Example**: `SOUL_SCANNER_DELAY=2000`
- **Purpose**: Prevents rate limiting from Telegram

---

### 📝 Logging Configuration

#### LOG_LEVEL (Optional)
- **Default**: `info`
- **Description**: Minimum log level to output
- **Options**: `error`, `warn`, `info`, `verbose`, `debug`
- **Example**: `LOG_LEVEL=info`
- **Production**: Use `warn` or `error`

#### LOG_FILE (Optional)
- **Default**: `logs/demure.log`
- **Description**: Path to log file
- **Example**: `LOG_FILE=logs/demure.log`

---

### 🔐 Security Configuration

#### JWT_SECRET (Required)
- **Description**: Secret key for JWT token generation
- **Example**: `JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random`
- **How to generate**: Use a random string generator (32+ characters)
- **Security**: Never share this key, use different keys for different environments

---

### 📊 Analytics Configuration

#### EXCEL_EXPORT_LIMIT (Optional)
- **Default**: `10000`
- **Description**: Maximum number of records in Excel exports
- **Example**: `EXCEL_EXPORT_LIMIT=10000`
- **Purpose**: Prevents memory issues with large exports

#### TOKEN_RETENTION_DAYS (Optional)
- **Default**: `30`
- **Description**: Number of days to keep token data before cleanup
- **Example**: `TOKEN_RETENTION_DAYS=30`
- **Purpose**: Automatic data cleanup to manage database size

---

## 🚀 Quick Setup Guide

### 1. Copy Environment File
```bash
cp .env.example .env
```

### 2. Required Variables (Minimum Setup)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/demure-web-app
REDIS_URL=redis://localhost:6379

# Telegram (at least one account)
TELEGRAM_API_ID_1=your_api_id
TELEGRAM_API_HASH_1=your_api_hash
TELEGRAM_PHONE_1=+**********

# Security
JWT_SECRET=your_very_long_random_secret_key_here
```

### 3. Optional but Recommended
```env
# Server
PORT=3002
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000

# Soul Scanner
SOUL_SCANNER_BOT_USERNAME=@soul_scanner_bot

# Rate Limiting
REQUESTS_PER_MINUTE=30
SOUL_SCANNER_DELAY=2000

# Logging
LOG_LEVEL=info
```

## 🔍 Environment Validation

The application will validate required environment variables on startup and show helpful error messages if any are missing.

## 🌐 Production Considerations

- Use strong, unique JWT secrets
- Set appropriate CORS origins
- Use production database URLs
- Set LOG_LEVEL to 'warn' or 'error'
- Use environment-specific Redis/MongoDB instances
- Consider using environment variable management services
