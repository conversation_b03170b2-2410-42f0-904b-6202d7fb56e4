const { Sequelize } = require('sequelize');
require('dotenv').config();

async function testConnection() {
  console.log('🔍 Testing MySQL connection...');
  
  try {
    const sequelize = new Sequelize({
      host: process.env.MYSQL_HOST || 'localhost',
      port: process.env.MYSQL_PORT || 3306,
      database: process.env.MYSQL_DATABASE || 'demure_web_app',
      username: process.env.MYSQL_USERNAME || 'demure_user',
      password: process.env.MYSQL_PASSWORD || 'demure_password',
      dialect: 'mysql',
      logging: false
    });

    await sequelize.authenticate();
    console.log('✅ MySQL connection successful!');
    
    // Test creating a simple table
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Table creation test successful!');
    
    await sequelize.close();
    console.log('✅ Connection closed successfully!');
    
  } catch (error) {
    console.error('❌ MySQL connection failed:', error.message);
    console.log('\n📋 Troubleshooting:');
    console.log('1. Make sure MySQL is running');
    console.log('2. Check your .env file credentials');
    console.log('3. Verify database exists');
    console.log('4. Check if Docker containers are running: docker ps');
  }
}

testConnection();
