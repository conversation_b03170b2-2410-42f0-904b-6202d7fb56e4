const { Sequelize } = require('sequelize');
const Logger = require('../utils/logger');

class DatabaseService {
  constructor() {
    this.logger = new Logger();
    this.sequelize = null;
  }

  async connect() {
    try {
      const dbType = process.env.DATABASE_TYPE || 'sqlite';

      if (dbType === 'sqlite') {
        // SQLite configuration (no installation required)
        this.sequelize = new Sequelize({
          dialect: 'sqlite',
          storage: process.env.SQLITE_PATH || './database.sqlite',
          logging: (msg) => this.logger.debug(msg),
          define: {
            timestamps: true,
            underscored: true,
            freezeTableName: true
          }
        });
        this.logger.info('Using SQLite database');
      } else {
        // MySQL configuration
        this.sequelize = new Sequelize({
          host: process.env.MYSQL_HOST || 'localhost',
          port: process.env.MYSQL_PORT || 3306,
          database: process.env.MYSQL_DATABASE || 'demure_web_app',
          username: process.env.MYSQL_USERNAME || 'demure_user',
          password: process.env.MYSQL_PASSWORD || 'demure_password',
          dialect: 'mysql',

          // Connection pool settings
          pool: {
            max: 10,
            min: 0,
            acquire: 30000,
            idle: 10000
          },

          // Logging
          logging: (msg) => this.logger.debug(msg),

          // Additional options
          define: {
            timestamps: true,
            underscored: true, // Use snake_case for column names
            freezeTableName: true // Don't pluralize table names
          }
        });
        this.logger.info('Using MySQL database');
      }

      // Test the connection
      await this.sequelize.authenticate();
      this.logger.info(`Connected to ${dbType.toUpperCase()} database successfully`);

      // Initialize models
      this.initializeModels();

      // Sync models (create tables if they don't exist)
      await this.sequelize.sync({ alter: false });
      this.logger.info('Database models synchronized');

      return this.sequelize;
    } catch (error) {
      this.logger.error('Failed to connect to MySQL:', error);
      throw error;
    }
  }

  initializeModels() {
    // Import and initialize models
    const Source = require('../models/Source')(this.sequelize);
    const Token = require('../models/Token')(this.sequelize);

    // Set up associations
    Source.hasMany(Token, { foreignKey: 'source_id', as: 'tokens' });
    Token.belongsTo(Source, { foreignKey: 'source_id', as: 'source' });

    // Make models globally accessible
    global.Source = Source;
    global.Token = Token;

    this.logger.info('Models initialized and made globally accessible');
  }

  async disconnect() {
    try {
      if (this.sequelize) {
        await this.sequelize.close();
        this.logger.info('Disconnected from database');
      }
    } catch (error) {
      this.logger.error('Error disconnecting from database:', error);
      throw error;
    }
  }

  async isConnected() {
    if (!this.sequelize) return false;
    try {
      await this.sequelize.authenticate();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getConnectionStatus() {
    if (!this.sequelize) {
      return { state: 'disconnected' };
    }

    try {
      await this.sequelize.authenticate();
      return {
        state: 'connected',
        host: this.sequelize.config.host,
        port: this.sequelize.config.port,
        database: this.sequelize.config.database
      };
    } catch (error) {
      return { state: 'disconnected', error: error.message };
    }
  }
}

module.exports = DatabaseService;
