// Use built-in fetch in Node.js 18+
// const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testAPI() {
  try {
    console.log('Testing POST /api/sources...');

    const response = await fetch('http://localhost:3002/api/sources', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Source',
        type: 'channel',
        target: '@testchannel'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());

    const data = await response.text();
    console.log('Response body:', data);

    if (response.ok) {
      console.log('✅ Source created successfully!');

      // Test GET
      console.log('\nTesting GET /api/sources...');
      const getResponse = await fetch('http://localhost:3002/api/sources');
      const sources = await getResponse.json();
      console.log('Sources:', sources);
    } else {
      console.log('❌ Failed to create source');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

testAPI();
