{"name": "demure-web-app", "version": "1.0.0", "description": "Multi-Source Token Analysis Platform with Soul Scanner Integration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "dev:frontend": "cd frontend && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run dev:frontend\"", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "install:all": "npm install && cd frontend && npm install", "setup": "node install.js", "test": "jest", "lint": "eslint src/", "clean": "rm -rf logs/* exports/* frontend/dist"}, "keywords": ["telegram", "token-analysis", "soul-scanner", "crypto", "pump-tokens"], "author": "Demure Team", "license": "MIT", "dependencies": {"bull": "^4.11.3", "concurrently": "^8.2.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "helmet": "^7.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "mysql2": "^3.14.1", "node-cron": "^3.0.2", "rate-limiter-flexible": "^2.4.2", "redis": "^4.6.8", "sequelize": "^6.37.7", "socket.io": "^4.7.2", "sqlite3": "^5.1.7", "telegram": "^2.19.8", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}