# 🚀 Demure Web App

**Multi-Source Token Analysis Platform with Soul Scanner Integration**

A powerful web application that listens to multiple Telegram sources (bots, channels, groups) to extract token addresses ending with "pump", analyzes them through Soul Scanner Bot, and provides comprehensive analytics with Excel export capabilities.

## 🎯 Features

### 📱 Multi-Source Support
- **Unlimited Sources**: Add bots, channels, and groups as token sources
- **Dedicated Accounts**: Each source uses its own Telegram account
- **Real-time Monitoring**: Live token extraction from all sources
- **Smart Filtering**: Automatically filter tokens ending with "pump"

### 🔍 Soul Scanner Integration
- **Automated Analysis**: Queue-based token analysis through Soul Scanner Bot
- **T-MC Tracking**: Multi-timeframe Top Market Cap monitoring (1H, 5H, 24H)
- **Profit Calculation**: Automatic profit percentage calculation based on T-MC progression
- **Rate Limiting**: Intelligent request management to avoid spam
- **Multiple Accounts**: Load balancing across Telegram accounts
- **Error Handling**: Automatic retry and failure management (2 attempts max, then N/A)

### 📊 Analytics Dashboard
- **Real-time Updates**: Live token discovery and analysis
- **Excel Export**: Generate detailed reports with T-MC tracking per source
- **Advanced Filtering**: Filter by price, market cap, volume, profit percentage, etc.
- **Performance Metrics**: Track source performance and T-MC success rates
- **Profit Analysis**: Visual profit highlighting in Excel (green for gains, red for losses)

### 🌐 Web Interface
- **Modern UI**: Clean, responsive dashboard
- **Source Management**: Easy source addition and configuration
- **Live Data**: Real-time updates via WebSocket
- **Export Tools**: Multiple export formats available

## 🛠️ Installation

### Prerequisites
- Node.js 16+
- MongoDB
- Redis
- Telegram API credentials

### Quick Start

1. **Clone and Install**
```bash
git clone <repository>
cd "Demure Web App"
npm run install:all
```

2. **Environment Setup**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start Services**
```bash
# Start MongoDB and Redis
# Then start the application
npm run dev
```

4. **Access Dashboard**
```
Backend: http://localhost:3002
Frontend: http://localhost:3000
```

## 📋 Configuration

### Telegram Accounts Setup
1. Go to https://my.telegram.org/apps
2. Create API credentials for each account
3. Add credentials to `.env` file
4. Run the app to generate session strings

### Adding Sources
1. Open the web dashboard
2. Click "Add Source"
3. Configure source details:
   - **Name**: Descriptive name
   - **Type**: Bot/Channel/Group
   - **Target**: Username or ID
   - **Account**: Telegram account to use

## 🏗️ Architecture

```
Web Dashboard → Source Manager → Telegram Pool → Token Extractor
                                                        ↓
Excel Generator ← Data Storage ← Soul Scanner ← Token Filter
```

## 📁 Project Structure

```
Demure Web App/
├── src/
│   ├── server.js              # Main server
│   ├── services/              # Core services
│   ├── models/                # Database models
│   ├── routes/                # API routes
│   └── utils/                 # Utilities
├── frontend/                  # Vue.js frontend
├── config/                    # Configuration files
├── logs/                      # Application logs
└── exports/                   # Excel exports
```

## 🚀 Usage

### Adding a Source
1. Navigate to Sources tab
2. Fill in source details
3. Select Telegram account
4. Click "Add Source"
5. Monitor real-time token extraction

### Analyzing Tokens
- Tokens are automatically queued for Soul Scanner analysis
- View progress in the dashboard
- Export results to Excel
- Apply filters for detailed analysis

### Managing Accounts
- Add multiple Telegram accounts for load balancing
- Monitor account usage and rate limits
- Automatic failover on account issues

## 📊 API Endpoints

- `POST /api/sources` - Add new source
- `GET /api/sources` - List all sources
- `GET /api/tokens` - Get analyzed tokens
- `GET /api/analytics/excel/:sourceId` - Export Excel
- `GET /api/analytics/dashboard/:sourceId` - Get analytics

## 🔧 Advanced Configuration

### Rate Limiting
- Adjust `REQUESTS_PER_MINUTE` for API limits
- Configure `SOUL_SCANNER_DELAY` for bot delays

### Data Retention
- Set `TOKEN_RETENTION_DAYS` for data cleanup
- Configure `EXCEL_EXPORT_LIMIT` for export size

## 🛡️ Security

- JWT authentication for API access
- CORS protection
- Rate limiting on all endpoints
- Secure session management

## 📈 Monitoring

- Real-time source statistics
- Token discovery metrics
- Account usage tracking
- Error rate monitoring

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs in `/logs` directory

---

**Built with ❤️ for the crypto community**
