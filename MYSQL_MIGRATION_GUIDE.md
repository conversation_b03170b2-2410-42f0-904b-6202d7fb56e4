# MySQL Migration Guide for Demure Web App

## Overview
This guide explains how to convert the Demure Web App from MongoDB to MySQL.

## Required Dependencies

### Remove MongoDB Dependencies
```bash
npm uninstall mongoose
```

### Install MySQL Dependencies
```bash
npm install mysql2 sequelize sequelize-cli
```

## Database Schema Conversion

### 1. Sources Table
```sql
CREATE TABLE sources (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  type ENUM('bot', 'channel', 'group') NOT NULL,
  target VARCHAR(255) NOT NULL,
  telegram_account VARCHAR(255) NOT NULL,
  extraction_pattern VARCHAR(255) DEFAULT '[A-Za-z0-9]{32,}pump',
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Configuration as JSON column
  configuration JSON,
  
  -- Statistics
  total_messages INT DEFAULT 0,
  tokens_found INT DEFAULT 0,
  tokens_analyzed INT DEFAULT 0,
  last_message_at TIMESTAMP NULL,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_type (type),
  INDEX idx_active (is_active),
  INDEX idx_telegram_account (telegram_account)
);
```

### 2. Tokens Table
```sql
CREATE TABLE tokens (
  id INT PRIMARY KEY AUTO_INCREMENT,
  address VARCHAR(255) UNIQUE NOT NULL,
  source_id INT NOT NULL,
  
  -- Basic token information
  symbol VARCHAR(20),
  name VARCHAR(100),
  
  -- Financial data
  price DECIMAL(20, 10) DEFAULT 0,
  market_cap VARCHAR(50),
  top_market_cap VARCHAR(50),
  volume_24h VARCHAR(50),
  holders INT DEFAULT 0,
  liquidity VARCHAR(50),
  fdv VARCHAR(50),
  
  -- Risk analysis
  risk_score DECIMAL(3, 1) DEFAULT 0,
  risk_factors JSON,
  is_flagged BOOLEAN DEFAULT FALSE,
  
  -- Analysis status
  analysis_status ENUM('pending', 'processing', 'completed', 'failed', 'timeout') DEFAULT 'pending',
  analysis_attempts INT DEFAULT 0,
  last_analysis_attempt TIMESTAMP NULL,
  analysis_error JSON,
  
  -- Soul Scanner response
  soul_scanner_response JSON,
  
  -- Discovery information
  discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  discovered_message JSON,
  
  -- T-MC tracking
  rescan_status JSON,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (source_id) REFERENCES sources(id) ON DELETE CASCADE,
  INDEX idx_address (address),
  INDEX idx_source_id (source_id),
  INDEX idx_analysis_status (analysis_status),
  INDEX idx_discovered_at (discovered_at),
  INDEX idx_risk_score (risk_score)
);
```

## Code Changes Required

### 1. Database Service (src/services/database-service.js)
- Replace mongoose connection with Sequelize
- Update connection methods
- Handle MySQL-specific configurations

### 2. Models (src/models/)
- Convert Mongoose schemas to Sequelize models
- Update relationships and associations
- Handle JSON fields for complex data

### 3. Queries
- Convert MongoDB aggregation pipelines to SQL
- Update all find/update/delete operations
- Modify analytics queries in routes/analytics.js

### 4. Environment Variables
Add MySQL configuration to .env:
```
# Database (MySQL)
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=demure_web_app
MYSQL_USERNAME=your_username
MYSQL_PASSWORD=your_password
MYSQL_DIALECT=mysql
```

## Migration Steps

1. **Install Dependencies**
   ```bash
   npm uninstall mongoose
   npm install mysql2 sequelize sequelize-cli
   ```

2. **Create Database**
   ```sql
   CREATE DATABASE demure_web_app;
   ```

3. **Update Environment Variables**
   - Replace MONGODB_URI with MySQL credentials

4. **Convert Models**
   - Replace each Mongoose model with Sequelize equivalent

5. **Update Database Service**
   - Replace mongoose connection with Sequelize

6. **Convert Queries**
   - Update all database operations to use Sequelize syntax

7. **Test Migration**
   - Run application with test data
   - Verify all functionality works

## Benefits of MySQL Migration

- **ACID Compliance**: Better data consistency
- **Mature Ecosystem**: More tools and hosting options
- **SQL Queries**: More familiar query language
- **Performance**: Better for complex analytical queries
- **Backup/Recovery**: More robust backup solutions

## Considerations

- **JSON Support**: MySQL 5.7+ required for JSON columns
- **Migration Time**: Existing data needs to be migrated
- **Query Complexity**: Some MongoDB aggregations need rewriting
- **Indexing**: Different indexing strategies required
