<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <h1>Page Not Found</h1>
      <p>The page you're looking for doesn't exist or has been moved.</p>
      
      <div class="actions">
        <button @click="goHome" class="home-btn">Go to Dashboard</button>
        <button @click="goBack" class="back-btn">Go Back</button>
      </div>
      
      <div class="suggestions">
        <h3>You might be looking for:</h3>
        <ul>
          <li><router-link to="/dashboard">Dashboard</router-link></li>
          <li><router-link to="/sources">Sources Management</router-link></li>
          <li><router-link to="/tokens">Token Analysis</router-link></li>
          <li><router-link to="/analytics">Analytics & Reports</router-link></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFound',
  methods: {
    goHome() {
      this.$router.push('/dashboard')
    },
    goBack() {
      this.$router.back()
    }
  }
}
</script>

<style scoped>
.not-found-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #e0e0e0;
  line-height: 1;
  margin-bottom: 20px;
}

h1 {
  font-size: 32px;
  color: #333;
  margin-bottom: 15px;
}

p {
  color: #666;
  font-size: 16px;
  margin-bottom: 30px;
}

.actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 40px;
}

.home-btn,
.back-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.home-btn {
  background: #1976d2;
  color: white;
}

.home-btn:hover {
  background: #1565c0;
}

.back-btn {
  background: #f5f5f5;
  color: #333;
}

.back-btn:hover {
  background: #e0e0e0;
}

.suggestions {
  text-align: left;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
}

.suggestions h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.suggestions li {
  margin-bottom: 8px;
}

.suggestions a {
  color: #1976d2;
  text-decoration: none;
  transition: color 0.2s;
}

.suggestions a:hover {
  color: #1565c0;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }
  
  h1 {
    font-size: 24px;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .home-btn,
  .back-btn {
    width: 200px;
  }
}
</style>
