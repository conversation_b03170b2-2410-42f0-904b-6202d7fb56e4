<template>
  <div class="settings-page">
    <div class="page-header">
      <h1>Settings</h1>
      <p>Configure your application settings</p>
    </div>

    <div class="settings-sections">
      <div class="settings-card">
        <h3>Database Configuration</h3>
        <div class="setting-item">
          <label>Database Type:</label>
          <span class="value">{{ dbConfig.type || 'SQLite' }}</span>
        </div>
        <div class="setting-item">
          <label>Connection Status:</label>
          <span :class="['status', dbConfig.connected ? 'connected' : 'disconnected']">
            {{ dbConfig.connected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>

      <div class="settings-card">
        <h3>Telegram Configuration</h3>
        <div class="setting-item">
          <label>Active Accounts:</label>
          <span class="value">{{ telegramConfig.accounts || 0 }}</span>
        </div>
        <div class="setting-item">
          <label>Connection Status:</label>
          <span :class="['status', telegramConfig.connected ? 'connected' : 'disconnected']">
            {{ telegramConfig.connected ? 'Connected' : 'Disconnected' }}
          </span>
        </div>
      </div>

      <div class="settings-card">
        <h3>Analysis Settings</h3>
        <form @submit.prevent="saveAnalysisSettings">
          <div class="form-group">
            <label>Soul Scanner Timeout (ms):</label>
            <input v-model.number="analysisSettings.timeout" type="number" min="5000" max="60000">
          </div>
          <div class="form-group">
            <label>Max Analysis Attempts:</label>
            <input v-model.number="analysisSettings.maxAttempts" type="number" min="1" max="10">
          </div>
          <div class="form-group">
            <label>Request Delay (ms):</label>
            <input v-model.number="analysisSettings.delay" type="number" min="1000" max="10000">
          </div>
          <button type="submit" class="save-btn">Save Settings</button>
        </form>
      </div>

      <div class="settings-card">
        <h3>Rate Limiting</h3>
        <form @submit.prevent="saveRateSettings">
          <div class="form-group">
            <label>Requests per Minute:</label>
            <input v-model.number="rateSettings.requestsPerMinute" type="number" min="1" max="100">
          </div>
          <div class="form-group">
            <label>Burst Limit:</label>
            <input v-model.number="rateSettings.burstLimit" type="number" min="1" max="50">
          </div>
          <button type="submit" class="save-btn">Save Settings</button>
        </form>
      </div>

      <div class="settings-card">
        <h3>Data Management</h3>
        <div class="setting-item">
          <label>Total Tokens:</label>
          <span class="value">{{ dataStats.totalTokens || 0 }}</span>
        </div>
        <div class="setting-item">
          <label>Database Size:</label>
          <span class="value">{{ dataStats.dbSize || 'Unknown' }}</span>
        </div>
        <div class="danger-zone">
          <h4>Danger Zone</h4>
          <button @click="confirmCleanup" class="danger-btn">Clean Old Data</button>
          <button @click="confirmReset" class="danger-btn">Reset Database</button>
        </div>
      </div>

      <div class="settings-card">
        <h3>System Information</h3>
        <div class="setting-item">
          <label>Node.js Version:</label>
          <span class="value">{{ systemInfo.nodeVersion || 'Unknown' }}</span>
        </div>
        <div class="setting-item">
          <label>App Version:</label>
          <span class="value">{{ systemInfo.appVersion || '1.0.0' }}</span>
        </div>
        <div class="setting-item">
          <label>Uptime:</label>
          <span class="value">{{ formatUptime(systemInfo.uptime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Settings',
  data() {
    return {
      dbConfig: {},
      telegramConfig: {},
      analysisSettings: {
        timeout: 30000,
        maxAttempts: 3,
        delay: 2000
      },
      rateSettings: {
        requestsPerMinute: 30,
        burstLimit: 10
      },
      dataStats: {},
      systemInfo: {}
    }
  },
  async mounted() {
    await this.loadSettings()
  },
  methods: {
    async loadSettings() {
      try {
        // Load health status
        const healthResponse = await fetch('/api/health')
        const health = await healthResponse.json()
        
        this.dbConfig = {
          type: 'SQLite',
          connected: health.services?.database || false
        }
        
        this.telegramConfig = {
          accounts: health.services?.telegram?.connectedClients || 0,
          connected: health.services?.telegram?.status === 'connected'
        }
        
        this.systemInfo = {
          nodeVersion: process.version,
          appVersion: '1.0.0',
          uptime: health.uptime
        }
        
        // Load data stats
        const statsResponse = await fetch('/api/analytics/summary')
        this.dataStats = await statsResponse.json()
        
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    },
    async saveAnalysisSettings() {
      try {
        const response = await fetch('/api/settings/analysis', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.analysisSettings)
        })
        
        if (response.ok) {
          alert('Analysis settings saved successfully!')
        }
      } catch (error) {
        console.error('Failed to save analysis settings:', error)
        alert('Failed to save settings')
      }
    },
    async saveRateSettings() {
      try {
        const response = await fetch('/api/settings/rate-limit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.rateSettings)
        })
        
        if (response.ok) {
          alert('Rate limit settings saved successfully!')
        }
      } catch (error) {
        console.error('Failed to save rate settings:', error)
        alert('Failed to save settings')
      }
    },
    confirmCleanup() {
      if (confirm('This will remove tokens older than 30 days. Continue?')) {
        this.cleanupOldData()
      }
    },
    confirmReset() {
      if (confirm('This will delete ALL data. This action cannot be undone. Continue?')) {
        this.resetDatabase()
      }
    },
    async cleanupOldData() {
      try {
        const response = await fetch('/api/admin/cleanup', { method: 'POST' })
        if (response.ok) {
          alert('Old data cleaned successfully!')
          await this.loadSettings()
        }
      } catch (error) {
        console.error('Failed to cleanup data:', error)
        alert('Failed to cleanup data')
      }
    },
    async resetDatabase() {
      try {
        const response = await fetch('/api/admin/reset', { method: 'POST' })
        if (response.ok) {
          alert('Database reset successfully!')
          await this.loadSettings()
        }
      } catch (error) {
        console.error('Failed to reset database:', error)
        alert('Failed to reset database')
      }
    },
    formatUptime(seconds) {
      if (!seconds) return 'Unknown'
      
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`
      } else {
        return `${minutes}m`
      }
    }
  }
}
</script>

<style scoped>
.settings-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.settings-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.settings-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-card h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.setting-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.setting-item label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
}

.status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status.connected {
  background: #e8f5e8;
  color: #2e7d32;
}

.status.disconnected {
  background: #ffebee;
  color: #c62828;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #666;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.save-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background: #1565c0;
}

.danger-zone {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #ffebee;
}

.danger-zone h4 {
  color: #c62828;
  margin: 0 0 15px 0;
}

.danger-btn {
  background: #f44336;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 10px;
  margin-bottom: 10px;
  transition: background-color 0.2s;
}

.danger-btn:hover {
  background: #d32f2f;
}

@media (max-width: 768px) {
  .settings-sections {
    grid-template-columns: 1fr;
  }
}
</style>
