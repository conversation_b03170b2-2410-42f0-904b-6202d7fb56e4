/* Global Styles for Demure Web App */

:root {
  --primary-color: #366092;
  --secondary-color: #4a90e2;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  --bg-color: #f5f7fa;
  --card-bg: #ffffff;
  --border-color: #dcdfe6;
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  
  --border-radius: 8px;
  --border-radius-small: 4px;
  --border-radius-large: 12px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: var(--bg-color);
  color: var(--text-primary);
  line-height: 1.6;
}

#app {
  min-height: 100vh;
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background: var(--card-bg);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-base);
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow-x: auto;
}

/* Cards */
.stats-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--shadow-light);
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.stats-card h3 {
  color: var(--text-primary);
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

/* Status indicators */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius-small);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-active {
  background-color: #f0f9ff;
  color: var(--success-color);
  border: 1px solid #67c23a;
}

.status-pending {
  background-color: #fef3cd;
  color: var(--warning-color);
  border: 1px solid #e6a23c;
}

.status-failed {
  background-color: #fef2f2;
  color: var(--danger-color);
  border: 1px solid #f56c6c;
}

.status-paused {
  background-color: #f5f5f5;
  color: var(--info-color);
  border: 1px solid #909399;
}

/* Risk score indicators */
.risk-low {
  color: var(--success-color);
  font-weight: 600;
}

.risk-medium {
  color: var(--warning-color);
  font-weight: 600;
}

.risk-high {
  color: var(--danger-color);
  font-weight: 600;
}

/* Tables */
.data-table {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.data-table .el-table__header {
  background-color: #fafafa;
}

.data-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* Buttons */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #2c5282;
  border-color: #2c5282;
}

/* Forms */
.form-container {
  background: var(--card-bg);
  padding: 24px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.form-section {
  margin-bottom: 24px;
}

.form-section h4 {
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
}

/* Metrics */
.metric-item {
  text-align: center;
  padding: 16px;
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Loading states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Responsive */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
  }
  
  .main-content {
    padding: 16px;
  }
  
  .stats-card {
    padding: 16px;
  }
}

/* Animations */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.rounded { border-radius: var(--border-radius); }
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded-lg { border-radius: var(--border-radius-large); }

.shadow { box-shadow: var(--shadow-base); }
.shadow-lg { box-shadow: var(--shadow-light); }
