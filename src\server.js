const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

// Import services
const DatabaseService = require('./services/database-service');
const TelegramClientPool = require('./services/telegram-client-pool');
const SourceManager = require('./services/source-manager');
const TokenAnalyzer = require('./services/token-analyzer');
const RescanService = require('./services/rescan-service');
const Logger = require('./utils/logger');

// Import routes
const sourcesRoutes = require('./routes/sources');
const tokensRoutes = require('./routes/tokens');
const analyticsRoutes = require('./routes/analytics');

class DemureWebApp {
  constructor() {
    this.app = express();
    this.server = http.createServer(this.app);
    this.io = socketIo(this.server, {
      cors: {
        origin: process.env.CORS_ORIGIN || "http://localhost:3000",
        methods: ["GET", "POST"]
      }
    });

    this.port = process.env.PORT || 3002;
    this.logger = new Logger();

    this.initializeServices();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupSocketHandlers();
  }

  async initializeServices() {
    try {
      // Initialize database
      this.database = new DatabaseService();
      await this.database.connect();

      // Initialize Telegram client pool
      this.telegramPool = new TelegramClientPool();
      await this.telegramPool.initialize();

      // Initialize source manager
      this.sourceManager = new SourceManager(this.telegramPool, this.io);

      // Initialize token analyzer
      this.tokenAnalyzer = new TokenAnalyzer(this.telegramPool, this.io);

      // Initialize rescan service for T-MC tracking
      this.rescanService = new RescanService(this.telegramPool, this.io);

      this.logger.info('All services initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet());

    // CORS
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || "http://localhost:3000",
      credentials: true
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use(express.static(path.join(__dirname, '../frontend/dist')));

    // Logging middleware
    this.app.use((req, res, next) => {
      this.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      next();
    });
  }

  setupRoutes() {
    // API routes
    this.app.use('/api/sources', sourcesRoutes(this.sourceManager));
    this.app.use('/api/tokens', tokensRoutes(this.tokenAnalyzer));
    this.app.use('/api/analytics', analyticsRoutes(this.tokenAnalyzer));

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        services: {
          database: this.database.isConnected(),
          telegram: this.telegramPool.getStatus(),
          sources: this.sourceManager.getActiveSourcesCount()
        }
      });
    });

    // Serve frontend for all other routes
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/dist/index.html'));
    });

    // Error handling
    this.app.use((error, req, res, next) => {
      this.logger.error('Express error:', error);
      res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
      });
    });
  }

  setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      this.logger.info('Client connected:', socket.id);

      // Send current stats
      socket.emit('stats', {
        sources: this.sourceManager.getStats(),
        tokens: this.tokenAnalyzer.getStats()
      });

      socket.on('disconnect', () => {
        this.logger.info('Client disconnected:', socket.id);
      });

      // Handle real-time requests
      socket.on('subscribe-source', (sourceId) => {
        socket.join(`source-${sourceId}`);
      });

      socket.on('unsubscribe-source', (sourceId) => {
        socket.leave(`source-${sourceId}`);
      });
    });
  }

  async start() {
    try {
      this.server.listen(this.port, () => {
        this.logger.info(`🚀 Demure Web App started on port ${this.port}`);
        this.logger.info(`📊 Dashboard: http://localhost:${this.port}`);
        this.logger.info(`🔗 API: http://localhost:${this.port}/api`);

        console.log(`
╔══════════════════════════════════════════════════════════════╗
║                    🚀 DEMURE WEB APP                         ║
║              Multi-Source Token Analysis Platform            ║
╠══════════════════════════════════════════════════════════════╣
║  📊 Dashboard: http://localhost:${this.port}                        ║
║  🔗 API:       http://localhost:${this.port}/api                    ║
║  📱 Sources:   ${this.sourceManager.getActiveSourcesCount()} active                              ║
║  🤖 Accounts:  ${this.telegramPool.getClientCount()} connected                          ║
╚══════════════════════════════════════════════════════════════╝
        `);
      });

      // Graceful shutdown
      process.on('SIGTERM', () => this.shutdown());
      process.on('SIGINT', () => this.shutdown());

    } catch (error) {
      this.logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async shutdown() {
    this.logger.info('Shutting down Demure Web App...');

    try {
      // Close server
      this.server.close();

      // Disconnect services
      await this.telegramPool.disconnect();
      await this.database.disconnect();

      this.logger.info('Shutdown complete');
      process.exit(0);
    } catch (error) {
      this.logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Start the application
const app = new DemureWebApp();
app.start().catch(error => {
  console.error('Failed to start application:', error);
  process.exit(1);
});

module.exports = DemureWebApp;
